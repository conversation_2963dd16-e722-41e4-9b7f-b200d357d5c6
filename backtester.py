#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精细化回测模块 - 多优先级、分时段交易策略
"""

import pandas as pd
from datetime import datetime
from tabulate import tabulate

# ==================== 回测配置参数 ====================
# 总资产配置
TOTAL_CAPITAL = 2000000  # 总资产200万

# 信号级别配置
# S级信号配置 (最高优先级)
S_LEVEL_SINGLE_AMOUNT = 100000      # S级单个股票买入金额: 10万
S_LEVEL_POSITION_RATIO = 0.50       # S级信号仓位比例: 50%

# A级信号配置 (中等优先级)
A_LEVEL_SINGLE_AMOUNT = 70000      # A级单个股票买入金额: 7万
A_LEVEL_POSITION_RATIO = 0.35       # A级信号仓位比例: 35%

# B级信号配置 (低优先级)
B_LEVEL_SINGLE_AMOUNT = 50000       # B级单个股票买入金额: 3万
B_LEVEL_POSITION_RATIO = 0.15       # B级信号仓位比例: 15%

# 风险控制参数
MIN_CASH_RESERVE = 50000            # 最低现金储备: 5万
MAX_SINGLE_POSITION = 1000000       # 单个股票最大持仓金额: 100万
MIN_TRADE_AMOUNT = 10000            # 最小交易金额: 1万

# 信号级别过滤配置
ENABLED_SIGNAL_LEVELS = ['S', 'A']  # 允许交易的信号级别: ['S', 'A'] 或 ['S', 'A', 'B'] 或 'all'
                                    # 默认只交易S级和A级信号，B级信号将被跳过
                                    # 设置为 'all' 或 ['S', 'A', 'B'] 可交易所有级别

# ====================================================

class Backtester:
    """
    精细化回测器 - 实现多优先级、分时段的交易策略
    """

    def __init__(self):
        """初始化回测器"""
        self.total_capital = TOTAL_CAPITAL  # 总资产，用于计算仓位
        self.cash = TOTAL_CAPITAL  # 可用现金
        self.positions = {}  # 持仓信息 { '股票代码': {'name': '股票名称', 'shares': 数量, 'buy_price': 成本价, 'buy_time': 买入时间, 'cost': 总成本} }
        self.trade_log = []  # 交易记录 [{'type': 'BUY', 'code', 'name', 'time', 'price', 'shares', 'amount'}]
        self.daily_pnl_log = []  # 每日盈亏记录

    def get_buy_amount_by_signal_level(self, signal_level):
        """
        根据信号级别获取买入金额

        参数:
        - signal_level: 信号级别 ('S', 'A', 'B')

        返回:
        - int: 买入金额
        """
        if signal_level == 'S':
            return S_LEVEL_SINGLE_AMOUNT
        elif signal_level == 'A':
            return A_LEVEL_SINGLE_AMOUNT
        elif signal_level == 'B':
            return B_LEVEL_SINGLE_AMOUNT
        else:
            raise ValueError(f"未知的信号级别: {signal_level}")

    def get_position_ratio_by_signal_level(self, signal_level):
        """
        根据信号级别获取仓位比例

        参数:
        - signal_level: 信号级别 ('S', 'A', 'B')

        返回:
        - float: 仓位比例
        """
        if signal_level == 'S':
            return S_LEVEL_POSITION_RATIO
        elif signal_level == 'A':
            return A_LEVEL_POSITION_RATIO
        elif signal_level == 'B':
            return B_LEVEL_POSITION_RATIO
        else:
            raise ValueError(f"未知的信号级别: {signal_level}")

    def can_trade(self):
        """
        检查是否可以进行交易

        返回:
        - bool: 是否可以交易
        """
        return self.cash >= (MIN_CASH_RESERVE + MIN_TRADE_AMOUNT)

    def is_signal_level_enabled(self, signal_level):
        """
        检查信号级别是否被启用

        参数:
        - signal_level: 信号级别 ('S', 'A', 'B')

        返回:
        - bool: 是否启用该信号级别
        """
        if ENABLED_SIGNAL_LEVELS == 'all':
            return True
        elif isinstance(ENABLED_SIGNAL_LEVELS, list):
            return signal_level in ENABLED_SIGNAL_LEVELS
        else:
            return False

    def buy_by_signal_level(self, stock_code, stock_name, price, buy_time, signal_level):
        """
        根据信号级别执行买入操作

        参数:
        - stock_code: 股票代码
        - stock_name: 股票名称
        - price: 买入价格
        - buy_time: 买入时间
        - signal_level: 信号级别 ('S', 'A', 'B')

        返回:
        - bool: 是否成功买入
        """
        try:
            # 检查信号级别是否被启用
            if not self.is_signal_level_enabled(signal_level):
                print(f"⚠️ {signal_level}级信号已被禁用，跳过 {stock_name}")
                return False
            # 检查是否可以交易
            if not self.can_trade():
                print(f"⚠️ 现金不足，无法交易 {stock_name} (剩余现金: {self.cash:,.0f}，需要保留: {MIN_CASH_RESERVE:,.0f})")
                return False

            # 检查是否已持有该股票
            if stock_code in self.positions:
                print(f"⚠️ 已持有 {stock_name}，跳过重复买入")
                return False

            # 获取买入金额
            amount_to_spend = self.get_buy_amount_by_signal_level(signal_level)

            # 检查现金是否充足
            if self.cash < amount_to_spend + MIN_CASH_RESERVE:
                print(f"⚠️ 现金不足，无法买入 {stock_name}，需要 {amount_to_spend:,.0f}，可用 {self.cash - MIN_CASH_RESERVE:,.0f}")
                return False

            # 检查单个持仓限制
            if amount_to_spend > MAX_SINGLE_POSITION:
                amount_to_spend = MAX_SINGLE_POSITION
                print(f"⚠️ 买入金额超过单个持仓限制，调整为 {amount_to_spend:,.0f}")

            # 计算可购买的股数（向下取整到100股）
            shares = int(amount_to_spend / price / 100) * 100

            if shares <= 0:
                print(f"⚠️ 股价过高，无法买入足够股数: {stock_name} @{price:.2f}")
                return False

            # 计算实际成本
            actual_cost = shares * price

            # 执行买入
            self.cash -= actual_cost
            self.positions[stock_code] = {
                'name': stock_name,
                'shares': shares,
                'buy_price': price,
                'buy_time': buy_time,
                'cost': actual_cost,
                'signal_level': signal_level
            }

            # 记录交易
            self.trade_log.append({
                'type': 'BUY',
                'code': stock_code,
                'name': stock_name,
                'time': buy_time,
                'price': price,
                'shares': shares,
                'amount': actual_cost,
                'signal_level': signal_level
            })

            print(f"✅ {signal_level}级信号买入成功: {stock_name}({stock_code}) {shares:,}股 @{price:.2f} 成本{actual_cost:,.0f}")
            return True

        except Exception as e:
            print(f"❌ 买入失败 {stock_name}: {e}")
            return False

    def buy(self, stock_code, stock_name, price, buy_time, amount_to_spend):
        """
        执行买入操作
        
        参数:
        - stock_code: 股票代码
        - stock_name: 股票名称
        - price: 买入价格
        - buy_time: 买入时间
        - amount_to_spend: 计划投入金额
        
        返回:
        - bool: 是否成功买入
        """
        try:
            # 检查现金是否充足
            if self.cash < amount_to_spend:
                print(f"⚠️ 现金不足，无法买入 {stock_name}，需要 {amount_to_spend:,.0f}，可用 {self.cash:,.0f}")
                return False
            
            # 计算可购买的股数（向下取整到100股）
            shares = int(amount_to_spend / price / 100) * 100
            
            if shares <= 0:
                print(f"⚠️ 计算股数为0，无法买入 {stock_name}，价格 {price:.2f}，计划金额 {amount_to_spend:,.0f}")
                return False
            
            # 计算实际成本
            actual_cost = shares * price
            
            # 检查是否已持仓
            if stock_code in self.positions:
                print(f"⚠️ 已持有 {stock_name}，跳过重复买入")
                return False
            
            # 更新现金和持仓
            self.cash -= actual_cost
            self.positions[stock_code] = {
                'name': stock_name,
                'shares': shares,
                'buy_price': price,
                'buy_time': buy_time,
                'cost': actual_cost
            }
            
            # 记录交易日志
            trade_record = {
                'type': 'BUY',
                'code': stock_code,
                'name': stock_name,
                'time': buy_time,
                'price': price,
                'shares': shares,
                'amount': actual_cost
            }
            self.trade_log.append(trade_record)
            
            print(f"✅ 买入成功: {stock_name}({stock_code}) {shares}股 @{price:.2f} 成本{actual_cost:,.0f}")
            return True
            
        except Exception as e:
            print(f"❌ 买入失败 {stock_name}: {e}")
            return False
    
    def calculate_eod_pnl(self, eod_prices):
        """
        计算日终盈亏
        
        参数:
        - eod_prices: 收盘价字典 {'股票代码': 收盘价}
        
        返回:
        - tuple: (当日盈亏, 总权益)
        """
        try:
            total_market_value = 0
            position_details = []
            
            # 计算每个持仓的市值
            for stock_code, position in self.positions.items():
                if stock_code in eod_prices:
                    eod_price = eod_prices[stock_code]
                    shares = position['shares']
                    buy_price = position['buy_price']
                    cost = position['cost']
                    
                    # 计算当前市值和盈亏
                    current_value = shares * eod_price
                    position_pnl = current_value - cost
                    position_pnl_pct = (position_pnl / cost) * 100 if cost > 0 else 0
                    
                    total_market_value += current_value
                    
                    position_details.append({
                        'code': stock_code,
                        'name': position['name'],
                        'shares': shares,
                        'buy_price': buy_price,
                        'eod_price': eod_price,
                        'cost': cost,
                        'market_value': current_value,
                        'pnl': position_pnl,
                        'pnl_pct': position_pnl_pct
                    })
                else:
                    print(f"⚠️ 未找到 {position['name']}({stock_code}) 的收盘价")
            
            # 计算总权益和盈亏
            total_equity = self.cash + total_market_value
            total_pnl = total_equity - self.total_capital
            total_pnl_pct = (total_pnl / self.total_capital) * 100
            
            # 记录每日盈亏
            daily_record = {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'cash': self.cash,
                'market_value': total_market_value,
                'total_equity': total_equity,
                'pnl': total_pnl,
                'pnl_pct': total_pnl_pct,
                'position_count': len(self.positions),
                'position_details': position_details
            }
            self.daily_pnl_log.append(daily_record)
            
            return total_pnl, total_equity, position_details
            
        except Exception as e:
            print(f"❌ 计算日终盈亏失败: {e}")
            return 0, self.total_capital, []
    
    def get_position_summary(self):
        """获取持仓汇总信息"""
        if not self.positions:
            return "无持仓"
        
        total_cost = sum(pos['cost'] for pos in self.positions.values())
        position_count = len(self.positions)
        cash_ratio = (self.cash / self.total_capital) * 100
        position_ratio = (total_cost / self.total_capital) * 100
        
        return f"持仓{position_count}只，成本{total_cost:,.0f}({position_ratio:.1f}%)，现金{self.cash:,.0f}({cash_ratio:.1f}%)"
    
    def generate_summary_report(self):
        """生成最终回测总结报告"""
        print("\n" + "=" * 80)
        print("📊 回测总结报告")
        print("=" * 80)
        
        # 基本信息
        print(f"💰 初始资金: {self.total_capital:,}")
        print(f"💵 剩余现金: {self.cash:,}")
        print(f"📈 交易次数: {len(self.trade_log)}")
        print(f"🎯 持仓数量: {len(self.positions)}")
        
        # 交易日志
        if self.trade_log:
            print(f"\n📋 交易记录:")
            trade_table = []
            for trade in self.trade_log:
                trade_table.append([
                    trade['time'],
                    trade['name'],
                    trade['code'],
                    f"{trade['shares']:,}",
                    f"{trade['price']:.2f}",
                    f"{trade['amount']:,.0f}"
                ])
            
            print(tabulate(trade_table, 
                         headers=['时间', '股票名称', '代码', '股数', '价格', '金额'],
                         tablefmt='grid'))
        
        # 当前持仓
        if self.positions:
            print(f"\n🎯 当前持仓:")
            position_table = []
            total_cost = 0
            for code, pos in self.positions.items():
                position_table.append([
                    pos['name'],
                    code,
                    f"{pos['shares']:,}",
                    f"{pos['buy_price']:.2f}",
                    f"{pos['cost']:,.0f}",
                    pos['buy_time']
                ])
                total_cost += pos['cost']
            
            print(tabulate(position_table,
                         headers=['股票名称', '代码', '股数', '成本价', '成本金额', '买入时间'],
                         tablefmt='grid'))
            print(f"持仓总成本: {total_cost:,.0f}")
        
        # 每日盈亏记录
        if self.daily_pnl_log:
            print(f"\n📈 每日盈亏记录:")
            daily_table = []
            for record in self.daily_pnl_log:
                daily_table.append([
                    record['date'],
                    f"{record['cash']:,.0f}",
                    f"{record['market_value']:,.0f}",
                    f"{record['total_equity']:,.0f}",
                    f"{record['pnl']:+,.0f}",
                    f"{record['pnl_pct']:+.2f}%",
                    record['position_count']
                ])
            
            print(tabulate(daily_table,
                         headers=['日期', '现金', '市值', '总权益', '盈亏', '收益率', '持仓数'],
                         tablefmt='grid'))
        
        print("=" * 80)
