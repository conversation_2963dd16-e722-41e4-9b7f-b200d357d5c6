#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试价格数据集成功能 - 模拟真实场景
"""

import pandas as pd
import os
import sys

# 导入必要的函数
sys.path.append('.')
from backtestv8 import execute_buy_signals, BacktestEngine

def create_mock_sector_data():
    """创建模拟的板块内部数据"""
    return pd.DataFrame({
        '序号': [1, 2, 3],
        '代码': ['300724', '600481', '600438'],
        '名称': ['捷佳伟创', '双良节能', '通威股份'],
        '最新价': [25.50, 18.30, 22.80],
        '今天涨跌幅': [5.2, 3.1, 2.8],
        '今日主力净流入-净额': [496000000, 419000000, 120000000]
    })

def create_mock_main_price_data():
    """创建模拟的主要价格数据（全为0，模拟实际问题）"""
    return pd.DataFrame({
        '名称': ['捷佳伟创', '双良节能', '通威股份', '其他股票1', '其他股票2'],
        '代码': ['300724', '600481', '600438', '000001', '000002'],
        '最新价': [0, 0, 0, 0, 0],  # 模拟实际问题：价格全为0
        '今日主力净流入-净额': [496000000, 419000000, 120000000, 50000000, 30000000]
    })

def create_mock_buy_signals():
    """创建模拟的买入信号"""
    sector_data = create_mock_sector_data()
    
    return [
        {
            'stock_name': '捷佳伟创',
            'signal_type': '双龙头',
            'sector_name': '光伏设备',
            'sector_rank': 1,
            'reason': '光伏设备第1名-双龙头第1股',
            'sector_internal_data': sector_data
        },
        {
            'stock_name': '双良节能',
            'signal_type': '双龙头',
            'sector_name': '光伏设备',
            'sector_rank': 1,
            'reason': '光伏设备第1名-双龙头第2股',
            'sector_internal_data': sector_data
        }
    ]

def test_price_integration():
    """测试价格数据集成功能"""
    print("=" * 60)
    print("测试价格数据集成功能")
    print("=" * 60)
    
    # 创建模拟数据
    main_price_data = create_mock_main_price_data()
    buy_signals = create_mock_buy_signals()
    
    print("1. 主要价格数据（模拟问题：价格全为0）:")
    print(main_price_data[['名称', '最新价']])
    
    print("\n2. 板块内部数据（包含真实价格）:")
    sector_data = buy_signals[0]['sector_internal_data']
    print(sector_data[['名称', '最新价']])
    
    print("\n3. 买入信号:")
    for signal in buy_signals:
        print(f"   - {signal['reason']}: {signal['stock_name']}")
    
    # 创建回测引擎
    backtester = BacktestEngine()
    
    print("\n4. 执行买入信号测试:")
    print("-" * 40)
    
    # 执行买入信号
    from datetime import datetime
    current_time = datetime.now()
    
    successful_buys = execute_buy_signals(buy_signals, main_price_data, current_time, backtester)
    
    print(f"\n5. 测试结果:")
    print(f"   成功买入数量: {successful_buys}")
    print(f"   持仓数量: {len(backtester.positions)}")
    
    if backtester.positions:
        print("\n6. 持仓详情:")
        for code, position in backtester.positions.items():
            print(f"   {position['name']}({code}): {position['shares']}股 @{position['buy_price']:.2f}元")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_price_integration()
