# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np
import os
import re
from datetime import datetime, time
from tabulate import tabulate
import warnings
import sqlite3

# 导入概念和板块过滤器
from concept_sector_filter import filter_meaningful_concepts_and_sectors, get_meaningless_items, is_meaningful_concept

# --- 1. 配置区域 ---
# 数据根目录配置 - 请修改为您自己的数据文件夹路径
BASE_DATA_DIR = r'D:\dev\mootdx\adata\Gemini\fund_data'
# 回测日期配置 - 请修改为您想要复盘的日期
BACKTEST_DATE = '2025-08-04'

# 数据库路径配置 (用于获取股票-板块映射关系)
STOCK_BLOCK_DB_PATH = r'D:\dev\mootdx\stock_block_analysis.db'
STOCK_BLOCK_DB_FALLBACK = 'stock_block_analysis.db'

# 分析参数配置
MIN_SECTORS_FOR_ANALYSIS = 4  # 板块/概念排行榜至少需要多少条数据才能进行分析
ANALYSIS_TOP_N = 10  # 板块/概念断层分析时，关注前N名

# ==================== 回测买入信号配置参数 ====================
# 总资产配置
TOTAL_CAPITAL = 2000000  # 总资产200万

# 买入信号类型配置（可以单个也可以全部）
ENABLED_SIGNAL_TYPES = ['绝对龙头', '单股龙头', '双龙头', '三足鼎立']  # 启用的信号类型

# 不同信号类型的单个买入金额配置
SIGNAL_BUY_AMOUNTS = {
    '绝对龙头': 100000,    # 绝对龙头：10万/股
    '单股龙头': 100000,    # 单股龙头：10万/股  
    '双龙头': 70000,       # 双龙头：7万/股
    '三足鼎立': 30000      # 三足鼎立：3万/股
}

# 概念和行业排名过滤配置
MAX_CONCEPT_RANK = 5  # 只买入前3名的概念
MAX_INDUSTRY_RANK = 5  # 只买入前3名的行业

# 风险控制参数
MIN_CASH_RESERVE = 50000      # 最低现金储备: 5万
MAX_SINGLE_POSITION = 1000000  # 单个股票最大持仓金额: 100万
MIN_TRADE_AMOUNT = 10000      # 最小交易金额: 1万

# ====================================================

# --- 配置区域结束 ---

warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)
pd.set_option('mode.chained_assignment', None)


# ==============================================================================
# § 0. 回测引擎类 (参考 backtester.py)
# ==============================================================================

class BacktestEngine:
    """回测引擎 - 基于backtester.py，支持新信号类型"""
    
    def __init__(self):
        """初始化回测引擎"""
        self.total_capital = TOTAL_CAPITAL
        self.cash = TOTAL_CAPITAL
        self.positions = {}  # 持仓信息 { '股票代码': {'name': '股票名称', 'shares': 数量, 'buy_price': 成本价, 'buy_time': 买入时间, 'cost': 总成本, 'signal_type': 信号类型} }
        self.trade_log = []  # 交易记录
        self.daily_pnl_log = []  # 每日盈亏记录
        
    def can_trade(self):
        """检查是否可以进行交易"""
        return self.cash >= (MIN_CASH_RESERVE + MIN_TRADE_AMOUNT)
    
    def is_signal_type_enabled(self, signal_type):
        """检查信号类型是否被启用"""
        return signal_type in ENABLED_SIGNAL_TYPES
    
    def get_buy_amount_by_signal_type(self, signal_type):
        """根据信号类型获取买入金额"""
        return SIGNAL_BUY_AMOUNTS.get(signal_type, 50000)  # 默认5万
    
    def buy_by_signal_type(self, stock_code, stock_name, price, buy_time, signal_type):
        """根据信号类型执行买入操作"""
        try:
            # 检查信号类型是否被启用
            if not self.is_signal_type_enabled(signal_type):
                print(f"⚠️ {signal_type}信号已被禁用，跳过 {stock_name}")
                return False
                
            # 检查是否可以交易
            if not self.can_trade():
                print(f"⚠️ 现金不足，无法交易 {stock_name} (剩余现金: {self.cash:,.0f}，需要保留: {MIN_CASH_RESERVE:,.0f})")
                return False
            
            # 检查是否已持有该股票
            if stock_code in self.positions:
                print(f"⚠️ 已持有 {stock_name}，跳过重复买入")
                return False
            
            # 获取买入金额
            amount_to_spend = self.get_buy_amount_by_signal_type(signal_type)
            
            # 检查现金是否充足
            if self.cash < amount_to_spend + MIN_CASH_RESERVE:
                print(f"⚠️ 现金不足，无法买入 {stock_name}，需要 {amount_to_spend:,.0f}，可用 {self.cash - MIN_CASH_RESERVE:,.0f}")
                return False
            
            # 检查单个持仓限制
            if amount_to_spend > MAX_SINGLE_POSITION:
                amount_to_spend = MAX_SINGLE_POSITION
                print(f"⚠️ 买入金额超过单个持仓限制，调整为 {amount_to_spend:,.0f}")
            
            # 计算可购买的股数（向下取整到100股）
            shares = int(amount_to_spend / price / 100) * 100
            
            if shares <= 0:
                print(f"⚠️ 股价过高，无法买入足够股数: {stock_name} @{price:.2f}")
                return False
            
            # 计算实际成本
            actual_cost = shares * price
            
            # 执行买入
            self.cash -= actual_cost
            self.positions[stock_code] = {
                'name': stock_name,
                'shares': shares,
                'buy_price': price,
                'buy_time': buy_time,
                'cost': actual_cost,
                'signal_type': signal_type
            }
            
            # 记录交易
            self.trade_log.append({
                'type': 'BUY',
                'code': stock_code,
                'name': stock_name,
                'time': buy_time,
                'price': price,
                'shares': shares,
                'amount': actual_cost,
                'signal_type': signal_type
            })
            
            print(f"✅ {signal_type}买入成功: {stock_name}({stock_code}) {shares:,}股 @{price:.2f} 成本{actual_cost:,.0f}")
            return True
            
        except Exception as e:
            print(f"❌ 买入失败 {stock_name}: {e}")
            return False
    
    def calculate_eod_pnl(self, eod_prices):
        """计算日终盈亏"""
        try:
            total_market_value = 0
            position_details = []
            
            # 计算每个持仓的市值
            for stock_code, position in self.positions.items():
                if stock_code in eod_prices:
                    eod_price = eod_prices[stock_code]
                    shares = position['shares']
                    buy_price = position['buy_price']
                    cost = position['cost']
                    
                    # 计算当前市值和盈亏
                    current_value = shares * eod_price
                    position_pnl = current_value - cost
                    position_pnl_pct = (position_pnl / cost) * 100 if cost > 0 else 0
                    
                    total_market_value += current_value
                    
                    position_details.append({
                        'code': stock_code,
                        'name': position['name'],
                        'shares': shares,
                        'buy_price': buy_price,
                        'eod_price': eod_price,
                        'cost': cost,
                        'market_value': current_value,
                        'pnl': position_pnl,
                        'pnl_pct': position_pnl_pct,
                        'signal_type': position.get('signal_type', '未知')
                    })
                else:
                    print(f"⚠️ 未找到 {position['name']}({stock_code}) 的收盘价")
            
            # 计算总权益和盈亏
            total_equity = self.cash + total_market_value
            total_pnl = total_equity - self.total_capital
            total_pnl_pct = (total_pnl / self.total_capital) * 100
            
            # 记录每日盈亏
            daily_record = {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'cash': self.cash,
                'market_value': total_market_value,
                'total_equity': total_equity,
                'pnl': total_pnl,
                'pnl_pct': total_pnl_pct,
                'position_count': len(self.positions),
                'position_details': position_details
            }
            self.daily_pnl_log.append(daily_record)
            
            return total_pnl, total_equity, position_details
            
        except Exception as e:
            print(f"❌ 计算日终盈亏失败: {e}")
            return 0, self.total_capital, []
    
    def generate_summary_report(self):
        """生成最终回测总结报告"""
        print("\n" + "=" * 80)
        print("📊 回测总结报告")
        print("=" * 80)
        
        # 基本信息
        print(f"💰 初始资金: {self.total_capital:,}")
        print(f"💵 剩余现金: {self.cash:,}")
        print(f"📈 交易次数: {len(self.trade_log)}")
        print(f"🎯 持仓数量: {len(self.positions)}")
        
        # 交易日志
        if self.trade_log:
            print(f"\n📋 交易记录:")
            trade_table = []
            for trade in self.trade_log:
                trade_table.append([
                    trade['time'],
                    trade['name'],
                    trade['code'],
                    f"{trade['shares']:,}",
                    f"{trade['price']:.2f}",
                    f"{trade['amount']:,.0f}",
                    trade.get('signal_type', '未知')
                ])
            
            from tabulate import tabulate
            print(tabulate(trade_table, 
                         headers=['时间', '股票名称', '代码', '股数', '价格', '金额', '信号类型'],
                         tablefmt='grid'))
        
        # 当前持仓
        if self.positions:
            print(f"\n🎯 当前持仓:")
            position_table = []
            total_cost = 0
            for code, pos in self.positions.items():
                position_table.append([
                    pos['name'],
                    code,
                    f"{pos['shares']:,}",
                    f"{pos['buy_price']:.2f}",
                    f"{pos['cost']:,.0f}",
                    pos['buy_time'],
                    pos.get('signal_type', '未知')
                ])
                total_cost += pos['cost']
            
            print(tabulate(position_table,
                         headers=['股票名称', '代码', '股数', '成本价', '成本金额', '买入时间', '信号类型'],
                         tablefmt='grid'))
            print(f"持仓总成本: {total_cost:,.0f}")
        
        # 每日盈亏记录
        if self.daily_pnl_log:
            print(f"\n📈 每日盈亏记录:")
            daily_table = []
            for record in self.daily_pnl_log:
                daily_table.append([
                    record['date'],
                    f"{record['cash']:,.0f}",
                    f"{record['market_value']:,.0f}",
                    f"{record['total_equity']:,.0f}",
                    f"{record['pnl']:+,.0f}",
                    f"{record['pnl_pct']:+.2f}%",
                    record['position_count']
                ])
            
            print(tabulate(daily_table,
                         headers=['日期', '现金', '市值', '总权益', '盈亏', '收益率', '持仓数'],
                         tablefmt='grid'))
        
        print("=" * 80)


# ==============================================================================
# § A. 核心辅助函数 (从 dynamic_gap_detector.py 移植并优化)
# ==============================================================================

def convert_to_float(value):
    """将包含'亿'或'万'的字符串转换为浮点数"""
    if isinstance(value, (int, float)):
        return float(value)
    if isinstance(value, str):
        try:
            value = value.replace('%', '').replace(',', '')
            if '亿' in value:
                return float(value.replace('亿', '')) * 1e8
            elif '万' in value:
                return float(value.replace('万', '')) * 1e4
            return float(value)
        except (ValueError, TypeError):
            return 0.0
    return 0.0


def format_amount(amount):
    """将数字格式化为易读的中文金额字符串"""
    if pd.isna(amount): return 'N/A'
    amount = float(amount)
    if abs(amount) >= 1e8: return f"{amount / 1e8:.2f}亿"
    if abs(amount) >= 1e4: return f"{amount / 1e4:.2f}万"
    return f"{amount:.2f}"


def extract_timestamp_from_filename(filename):
    """从多种格式的文件名中提取时间戳 (HHMMSS)"""
    patterns = [
        r'_\d{8}_(\d{6})\.',  # e.g., fund_flow_rank_20250725_093047.csv
        r'_(\d{6})\.csv$',  # e.g., zt_pool_akshare_东方财富_093651.csv
        r'^(\d{2})-(\d{2})_'  # e.g., 09-30_zt_pool.csv -> HHMM00
    ]
    for pattern in patterns:
        match = re.search(pattern, filename)
        if match:
            if len(match.groups()) == 2:  # 匹配 HH-MM_ 格式
                return f"{match.group(1)}{match.group(2)}00"
            return match.group(1)
    return None


def find_latest_file(file_list, current_time):
    """在文件列表中查找不晚于当前模拟时间的最新文件"""
    latest_file = None
    latest_timestamp = None
    for f in sorted(file_list):
        ts_str = extract_timestamp_from_filename(f)
        if ts_str:
            try:
                file_time = datetime.strptime(ts_str, '%H%M%S').time()
                if file_time <= current_time:
                    if latest_timestamp is None or file_time > latest_timestamp:
                        latest_timestamp = file_time
                        latest_file = f
                else:
                    # 由于文件是排序的，一旦超过当前时间，后续的都会超过
                    break
            except ValueError:
                continue
    return latest_file


def get_stock_sectors(stock_name, stock_code=None):
    """从数据库获取股票的概念和行业信息"""
    db_path = STOCK_BLOCK_DB_PATH if os.path.exists(STOCK_BLOCK_DB_PATH) else STOCK_BLOCK_DB_FALLBACK
    if not os.path.exists(db_path):
        return {'concepts': [], 'industries': []}

    query = "SELECT concept_blocks, industry_blocks FROM stocks WHERE "
    params = ()
    if stock_code:
        clean_code = str(stock_code).split('.')[0]
        query += "stock_code = ?"
        params = (clean_code,)
    elif stock_name:
        query += "short_name = ?"
        params = (stock_name,)
    else:
        return {'concepts': [], 'industries': []}

    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            result = cursor.fetchone()
            if result:
                concepts = result[0].split(',') if result[0] else []
                industries = result[1].split(',') if result[1] else []
                
                # 【新增：过滤无意义概念和行业】
                filtered_concepts = filter_meaningful_concepts_and_sectors(concepts) if concepts else []
                filtered_industries = filter_meaningful_concepts_and_sectors(industries) if industries else []
                
                return {'concepts': filtered_concepts, 'industries': filtered_industries}
    except Exception as e:
        print(f"数据库查询失败 ({stock_name}): {e}")
    return {'concepts': [], 'industries': []}


# ==============================================================================
# § B. 个股资金流断层分析模块 (移植自 dynamic_gap_detector.py)
# ==============================================================================

def get_stock_filter_strategy(file_format):
    """根据文件格式确定过滤策略"""
    filter_strategies = {
        'fund_flow_rank_format': 'positive_and_gain', # 资金流排名：净流入>0且涨跌幅>=0
        'rank_format': 'positive_and_gain',   # 排名格式：净流入>0且涨跌幅>=0
        'individual_format': 'positive_only', # 个股资金流排名：只要净流入>0
        'tpdog_format': 'positive_only',      # tpdog：只要净流入>0
        'akshare_format': 'positive_only',    # akshare：只要净流入>0
        'stock_flow_format': 'positive_only', # 个股资金流通用：只要净流入>0
        'standard_format': 'positive_only'    # 标准：只要净流入>0
    }
    return filter_strategies.get(file_format, 'positive_only')

def apply_stock_filter(stock_flow_data, file_format):
    """根据文件格式应用对应的过滤策略"""
    strategy = get_stock_filter_strategy(file_format)
    
    if strategy == 'positive_and_gain':
        # 要求净流入>0且涨跌幅>=0
        if '今日涨跌幅' in stock_flow_data.columns:
            return stock_flow_data[
                (stock_flow_data['今日主力净流入-净额'] > 0) & 
                (stock_flow_data['今日涨跌幅'] >= 0)
            ]
        else:
            # 如果没有涨跌幅数据，退化为只要求净流入>0
            return stock_flow_data[stock_flow_data['今日主力净流入-净额'] > 0]
    else:
        # 默认策略：只要求净流入>0
        return stock_flow_data[stock_flow_data['今日主力净流入-净额'] > 0]

def classify_file_type(filename):
    """根据文件名分类文件类型，支持多种格式 - 简化版"""
    filename_lower = filename.lower()
    
    # 资金流排名格式
    if ('fund_flow_rank' in filename_lower or 
        'individual_fund_flow' in filename_lower or
        '个股资金流' in filename_lower):
        return 'fund_flow_rank_format'
    
    # tpdog格式
    if 'tpdog' in filename_lower:
        return 'tpdog_format'
    
    # akshare格式
    if 'akshare' in filename_lower:
        return 'akshare_format'
    
    # 涨停池格式
    if ('limit_up' in filename_lower or 'zt_pool' in filename_lower or '涨停' in filename_lower):
        return 'individual_format'
    
    # 个股资金流通用格式
    if ('stock_flow' in filename_lower or '个股' in filename_lower):
        return 'stock_flow_format'
    
    # 默认格式
    return 'standard_format'

def apply_column_mapping(df, column_mapping):
    """应用列名映射并验证必要列"""
    mapped_columns = {}
    available_columns = []
    
    print(f"[DEBUG] 原始文件列名: {list(df.columns)}")

    for target_col, possible_names in column_mapping.items():
        found = False
        for possible_name in possible_names:
            if possible_name in df.columns:
                mapped_columns[possible_name] = target_col
                available_columns.append(target_col)
                print(f"[DEBUG] 映射成功: {possible_name} -> {target_col}")
                found = True
                break
        if not found:
            print(f"[DEBUG] 未找到{target_col}列，尝试的列名: {possible_names}")

    # 检查必要列
    if '名称' not in available_columns:
        print(f"缺少股票名称列，可用列: {list(df.columns)}")
        return None

    # 重命名列并选择可用列
    df_clean = df.rename(columns=mapped_columns)
    df_clean = df_clean[available_columns].copy()

    # 如果没有代码列，添加空的代码列以保持兼容性
    if '代码' not in available_columns:
        print(f"[WARNING] 文件中未找到股票代码列，将添加空的代码列以保持兼容性")
        df_clean['代码'] = ''
    
    # 如果没有价格列，添加空的价格列以保持兼容性
    if '最新价' not in available_columns:
        print(f"[WARNING] 文件中未找到价格列，将添加空的价格列以保持兼容性")
        df_clean['最新价'] = 0.0

    print(f"[DEBUG] 处理后的列名: {list(df_clean.columns)}")
    return df_clean

def parse_tpdog_format(df):
    """处理tpdog格式文件"""
    column_mapping = {
        '名称': ['名称', 'name', '股票名称'],
        '代码': ['代码', 'code', '股票代码', '证券代码', 'symbol', 'stock_code'],
        '最新价': ['最新价', 'price', '现价', 'latest_price'],
        '今日涨跌幅': ['今日涨跌幅', '涨跌幅', 'change_pct'],
        '今日主力净流入-净额': ['今日主力净流入-净额', 'main_net_inflow', '主力净流入', '净流入'],
        '今日主力净流入-净占比': ['今日主力净流入-净占比', 'r_net', '主力净流入占比'],
        '换手率': ['换手率', 'turnover_rate', 'turnover']
    }
    
    df_processed = apply_column_mapping(df, column_mapping)
    return df_processed

def parse_standard_format(df):
    """处理标准格式文件"""
    column_mapping = {
        '名称': ['名称', 'name', '股票名称'],
        '代码': ['代码', 'code', '股票代码', '证券代码', 'symbol', 'stock_code'],
        '最新价': ['最新价', 'price', '现价', 'latest_price'],
        '今日涨跌幅': ['今日涨跌幅', '涨跌幅', 'change_pct'],
        '今日主力净流入-净额': ['今日主力净流入-净额', 'main_net_inflow', '主力净流入', '净流入'],
        '今日主力净流入-净占比': ['今日主力净流入-净占比', 'main_net_inflow_pct', '主力净流入占比'],
        '换手率': ['换手率', 'turnover_rate', 'turnover']
    }
    
    df_processed = apply_column_mapping(df, column_mapping)
    return df_processed

def standardize_stock_data(df, file_format):
    """根据文件格式标准化股票数据列名"""
    if file_format == 'tpdog_format':
        return parse_tpdog_format(df)
    else:
        return parse_standard_format(df)

# 点火检测相关常量
MIN_SECTORS_FOR_ANALYSIS = 5
SLIDING_WINDOW_MINUTES = 30
MIN_RANK_THRESHOLD = 20
MIN_PF_THRESHOLD = 1.25
WRA_MULTIPLIER = 1.5
CT_MULTIPLIER = 1.5

# 持续攻击检测常量
SUSTAINED_WINDOW_MINUTES = 60
SUSTAINED_RANK_THRESHOLD = 200
SUSTAINED_CUMULATIVE_INFLOW_RANK_BENCHMARK = 30
SUSTAINED_INFLOW_STABILITY_RATIO = 0.7
SUSTAINED_RANK_SLOPE_THRESHOLD = -0.5
SUSTAINED_MIN_DATA_POINTS = 5

class MarketPulseDataPool:
    """V3.0 市场脉搏实时数据池 - 滑动窗口管理器"""

    def __init__(self, window_minutes=30):
        self.window_minutes = window_minutes
        self.data_pool = []

    def add(self, stock_name, wra, ct, rank, timestamp):
        """添加新的数据点"""
        from datetime import datetime, timedelta
        
        if isinstance(timestamp, str):
            timestamp = datetime.strptime(timestamp, '%H:%M:%S')
        elif isinstance(timestamp, datetime):
            # 如果已经是datetime对象，直接使用
            pass
        else:
            # 如果是time对象，转换为datetime
            timestamp = datetime.combine(datetime.today(), timestamp)
        
        self.data_pool.append({
            'timestamp': timestamp,
            'stock_name': stock_name,
            'wra': wra,
            'ct': ct,
            'rank': rank
        })
        self._cleanup_old_data()

    def _cleanup_old_data(self):
        """清理过期数据"""
        from datetime import datetime, timedelta
        if not self.data_pool:
            return
        
        latest_time = max(entry['timestamp'] for entry in self.data_pool)
        cutoff_time = latest_time - timedelta(minutes=self.window_minutes)
        self.data_pool = [entry for entry in self.data_pool if entry['timestamp'] >= cutoff_time]

    def get_stock_history(self, stock_name):
        """获取特定股票的历史数据"""
        return [entry for entry in self.data_pool if entry['stock_name'] == stock_name]

class ObservationPool:
    """V5.1 持续性验证观察池 - 过滤毛刺信号"""
    
    def __init__(self, observation_minutes=15):
        from datetime import datetime, timedelta
        self.observation_minutes = observation_minutes
        self.pool = {}  # {stock_name: {'entry_time': datetime, 'data': {...}}}
        
    def add_candidate(self, stock_name, data, current_time):
        """将股票加入观察池"""
        from datetime import datetime
        if isinstance(current_time, str):
            current_time = datetime.strptime(current_time, '%H:%M:%S')
        elif isinstance(current_time, datetime):
            # 如果已经是datetime对象，直接使用
            pass
        else:
            # 如果是time对象，转换为datetime
            current_time = datetime.combine(datetime.today(), current_time)
            
        self.pool[stock_name] = {
            'entry_time': current_time,
            'data': data
        }
        self._cleanup_expired_candidates(current_time)
        
    def _cleanup_expired_candidates(self, current_time):
        """清理过期的候选股票"""
        from datetime import timedelta
        expired_stocks = []
        cutoff_time = current_time - timedelta(minutes=self.observation_minutes)
        
        for stock_name, info in self.pool.items():
            if info['entry_time'] < cutoff_time:
                expired_stocks.append(stock_name)
        
        for stock_name in expired_stocks:
            del self.pool[stock_name]

class StockFlowIgnitionDetector:
    """V7.0 个股资金流信号检测器 - "爆发点火"与"持续攻击"双模型矩阵版"""

    def __init__(self):
        # V5.1 及之前版本所需属性
        self.previous_snapshot = None
        self.market_pulse_pool = MarketPulseDataPool(SLIDING_WINDOW_MINUTES)
        self.observation_pool = ObservationPool()

        # V7.0 新增：用于"持续攻击"信号检测的长时间序列数据
        self.long_term_stock_data = {}  # {stock_name: [datapoint_1, datapoint_2, ...]}

        self.ignition_thresholds = {
            'min_rank': MIN_RANK_THRESHOLD,
            'min_pf': MIN_PF_THRESHOLD,
            'wra_multiplier': WRA_MULTIPLIER,
            'ct_multiplier': CT_MULTIPLIER,
            'min_score': 7.0
        }

        # V10.0 新增：板块映射缓存
        self.stock_board_mapping = None

    def _load_stock_board_mapping(self):
        """加载股票-板块映射关系"""
        # 简化版实现，避免复杂的文件依赖
        return None

    def detect_ignition_signals(self, current_data, current_time, market_snapshot=None):
        """V7.0 - 检测"爆发点火"与"持续攻击"两种信号"""
        if self.previous_snapshot is None:
            self.previous_snapshot = self._create_data_snapshot(current_data, current_time)
            self._update_long_term_data(current_data, current_time)
            return []

        # 检测爆发点火信号
        ignition_signals = self._detect_ignition_signals_v5(current_data, current_time, market_snapshot)
        
        # 检测持续攻击信号
        sustained_signals = self._detect_sustained_attack_signals(current_data, current_time)
        
        # 更新数据
        self.previous_snapshot = self._create_data_snapshot(current_data, current_time)
        self._update_long_term_data(current_data, current_time)
        
        # 合并信号
        all_signals = ignition_signals + sustained_signals
        return all_signals

    def _create_data_snapshot(self, current_data, current_time):
        """创建数据快照"""
        return {
            'timestamp': current_time,
            'data': current_data.copy() if not current_data.empty else None,
            'top_stock': current_data.iloc[0]['名称'] if not current_data.empty else None
        }

    def _update_long_term_data(self, current_data, current_time):
        """更新长期数据存储"""
        from datetime import datetime, timedelta
        
        # 为每只股票添加当前数据点
        for idx, row in current_data.iterrows():
            stock_name = row['名称']
            if stock_name not in self.long_term_stock_data:
                self.long_term_stock_data[stock_name] = []
            
            self.long_term_stock_data[stock_name].append({
                'timestamp': current_time,
                'rank': idx + 1,
                'inflow': row['今日主力净流入-净额'],
                'data': row.to_dict()
            })
        
        # 清理过期数据（保留60分钟）
        if isinstance(current_time, str):
            current_time = datetime.strptime(current_time, '%H:%M:%S')
        elif isinstance(current_time, datetime):
            # 如果已经是datetime对象，直接使用
            pass
        else:
            # 如果是time对象，转换为datetime
            current_time = datetime.combine(datetime.today(), current_time)
            
        cutoff_time = current_time - timedelta(minutes=SUSTAINED_WINDOW_MINUTES)
        
        for stock_name in list(self.long_term_stock_data.keys()):
            self.long_term_stock_data[stock_name] = [
                point for point in self.long_term_stock_data[stock_name]
                if isinstance(point['timestamp'], datetime) and point['timestamp'] >= cutoff_time
            ]
            
            # 如果没有数据了，删除该股票
            if not self.long_term_stock_data[stock_name]:
                del self.long_term_stock_data[stock_name]

    def _detect_ignition_signals_v5(self, current_data, current_time, market_snapshot=None):
        """检测爆发点火信号"""
        if self.previous_snapshot is None or self.previous_snapshot['data'] is None:
            return []
        
        signals = []
        previous_data = self.previous_snapshot['data']
        
        # 简化的点火检测逻辑
        for idx, row in current_data.head(20).iterrows():
            stock_name = row['名称']
            current_rank = idx + 1
            current_inflow = row['今日主力净流入-净额']
            
            # 查找在前一快照中的位置
            prev_row = previous_data[previous_data['名称'] == stock_name]
            if prev_row.empty:
                continue
                
            prev_rank = prev_row.index[0] + 1
            prev_inflow = prev_row.iloc[0]['今日主力净流入-净额']
            
            # 简单的点火条件：排名大幅提升且资金流入显著增加
            rank_improvement = prev_rank - current_rank
            inflow_ratio = current_inflow / prev_inflow if prev_inflow > 0 else 1
            
            if rank_improvement >= 5 and inflow_ratio >= 1.5:
                signal = {
                    'type': 'ignition',
                    'stock_name': stock_name,
                    'rank': current_rank,
                    'rank_improvement': rank_improvement,
                    'inflow_ratio': inflow_ratio,
                    'timestamp': current_time
                }
                signals.append(signal)
        
        return signals

    def _detect_sustained_attack_signals(self, current_data, current_time):
        """检测持续攻击信号"""
        sustained_signals = []
        
        for stock_name, history in self.long_term_stock_data.items():
            if len(history) < SUSTAINED_MIN_DATA_POINTS:
                continue
                
            # 检查是否在合理排名范围内
            latest_rank = history[-1]['rank']
            if latest_rank > SUSTAINED_RANK_THRESHOLD:
                continue
            
            # 检查资金流入稳定性
            positive_points = sum(1 for point in history if point['inflow'] > 0)
            stability_ratio = positive_points / len(history)
            
            if stability_ratio >= SUSTAINED_INFLOW_STABILITY_RATIO:
                signal = {
                    'type': 'sustained_attack',
                    'stock_name': stock_name,
                    'current_rank': latest_rank,
                    'stability_ratio': stability_ratio,
                    'window_points': len(history),
                    'timestamp': current_time
                }
                sustained_signals.append(signal)
        
        return sustained_signals

def analyze_market_state(inflows):
    """分析市场状态：资金规模、集中度、分散度"""
    inflows_yi = [x / 1e8 for x in inflows[:5]]
    total_top5 = sum(inflows_yi)

    if total_top5 > 80:
        scale = "超大资金"
    elif total_top5 > 40:
        scale = "大资金"
    else:
        scale = "小资金"

    concentration = sum(inflows_yi[:2]) / total_top5 if total_top5 > 0 else 0
    dispersion = np.std(inflows_yi) / np.mean(inflows_yi) if np.mean(inflows_yi) > 0 else 0

    return {"scale": scale, "concentration": concentration, "dispersion": dispersion,
            "total_top5": total_top5, "avg_top5": np.mean(inflows_yi)}


def calculate_dynamic_thresholds(market_state):
    """根据市场状态动态计算判断阈值"""
    scale_factors = {"超大资金": 1.15, "大资金": 1.25, "小资金": 1.45}
    concentration_adj = (1 - market_state["concentration"]) * 0.1
    min_rel_gap = scale_factors[market_state["scale"]] + concentration_adj
    min_abs_gap_ratio = 0.15 + market_state["dispersion"] * 0.1
    min_abs_gap = market_state["avg_top5"] * min_abs_gap_ratio

    return {"min_relative_gap": min_rel_gap, "min_absolute_gap": min_abs_gap,
            "group_cohesion_limit": 1.4, "min_gap_score": 1.5}


def find_all_gap_points(inflows):
    """全局扫描，计算所有潜在断层点的综合得分"""
    gap_scores = []
    for i in range(min(6, len(inflows) - 1)):
        abs_gap = inflows[i] - inflows[i + 1]
        rel_gap = inflows[i] / inflows[i + 1] if inflows[i + 1] > 0 else float('inf')
        position_weight = 1.0 / (i / 2 + 1)
        gap_score = (abs_gap / 1e8) * rel_gap * position_weight
        gap_scores.append({"position": i, "abs_gap": abs_gap, "rel_gap": rel_gap, "score": gap_score})

    return max(gap_scores, key=lambda x: x["score"]) if gap_scores else None


def identify_leading_group(inflows, gap_position, names):
    """根据断层点识别领先集团及其特征"""
    leading_group = inflows[:gap_position + 1]
    leading_names = names[:gap_position + 1]
    max_internal_gap = 1.0
    if len(leading_group) > 1:
        internal_gaps = [leading_group[j] / leading_group[j + 1] for j in range(len(leading_group) - 1)]
        max_internal_gap = max(internal_gaps) if internal_gaps else 1.0

    return {"size": len(leading_group), "members": leading_names, "internal_cohesion": max_internal_gap}


def comprehensive_evaluation(market_state, max_gap, group_info, thresholds):
    """多维度综合评判，计算最终得分"""
    scores = {}
    scores["relative"] = min(max_gap["rel_gap"] / thresholds["min_relative_gap"], 2.0)
    scores["absolute"] = min((max_gap["abs_gap"] / 1e8) / thresholds["min_absolute_gap"], 2.0)
    scores["cohesion"] = 2.0 if group_info["internal_cohesion"] <= thresholds["group_cohesion_limit"] else 0.0
    scores["pattern"] = 1.8 if market_state["concentration"] > 0.65 else 1.2
    scores["position"] = 2.0 * (1.0 / (max_gap["position"] / 2 + 1))

    weights = {"relative": 0.22, "absolute": 0.22, "cohesion": 0.18, "pattern": 0.18, "position": 0.20}
    return sum(scores[key] * weights[key] for key in weights)


def analyze_stock_flow_gap(df_stocks, current_time=None, data_dir=None, ignition_detector=None, file_format=None, market_snapshot=None):
    """个股资金流断层分析主函数 - 与dynamic_gap_detector.py兼容版本"""
    
    try:
        # 1. 数据预处理 - 使用格式感知的过滤策略
        if file_format:
            positive_flow_df = apply_stock_filter(df_stocks, file_format)
        else:
            # 兼容旧调用，使用默认过滤策略
            positive_flow_df = df_stocks[df_stocks['今日主力净流入-净额'] > 0]
            
        if len(positive_flow_df) < 5:
            return "【个股资金流分析】: 数据不足，无法分析。"

        # 2. 点火信号检测（使用传入的、持久化的检测器实例）
        if ignition_detector is None:
            # 如果外部没有提供检测器，创建一个临时的
            ignition_detector = StockFlowIgnitionDetector()

        # 确保时间类型正确
        if current_time is not None:
            print(f"[DEBUG] current_time类型: {type(current_time)}, 值: {current_time}")

        try:
            ignition_signals = ignition_detector.detect_ignition_signals(positive_flow_df, current_time, market_snapshot)
        except Exception as e:
            print(f"[ERROR] 点火信号检测失败: {e}")
            print(f"[ERROR] 详细错误: {str(e)}")
            ignition_signals = []
    except Exception as e:
        print(f"[ERROR] 数据预处理失败: {e}")
        return f"【个股资金流分析】: 分析失败 - {e}"

    inflows = positive_flow_df.head(50)['今日主力净流入-净额'].tolist()
    names = positive_flow_df.head(50)['名称'].tolist()

    market_state = analyze_market_state(inflows)
    thresholds = calculate_dynamic_thresholds(market_state)
    max_gap = find_all_gap_points(inflows)

    if not max_gap:
        return "【个股资金流分析】: 未找到潜在断层点。"

    group_info = identify_leading_group(inflows, max_gap["position"], names)
    total_score = comprehensive_evaluation(market_state, max_gap, group_info, thresholds)

    if total_score >= thresholds["min_gap_score"]:
        # 发现断层的详细报告
        report = [f"【★★★★★ 个股资金流发现资金断层! ★★★★★】"]
        leader_desc = f"断层龙头: 【{group_info['members'][0]}】" if group_info[
                                                                       "size"] == 1 else f"领先集团: 【{', '.join(group_info['members'])}】"
        report.append(f"  {leader_desc}")
        report.append(f"  断层位置: 在第 {max_gap['position'] + 1} 名后")
        report.append(f"  绝对差距: {format_amount(max_gap['abs_gap'])}")
        report.append(f"  相对差距: {max_gap['rel_gap']:.2f} 倍")
        report.append(f"  市场状态: {market_state['scale']}市场 (集中度 {market_state['concentration']:.1%})")
        report.append(f"  综合得分: {total_score:.2f} (阈值 {thresholds['min_gap_score']:.1f})")
        return "\n".join(report)
    else:
        # 未发现断层的报告（与dynamic_gap_detector.py保持一致）
        leader_name = group_info["members"][0] if group_info["members"] else "未知"
        report = [f"【--- 个股资金流未发现显著资金断层 ---】"]
        report.append(f"  当前龙头: 【{leader_name}】")
        report.append(f"  市场状态: {market_state['scale']}市场，集中度{market_state['concentration']:.1%}")
        report.append(f"  综合得分: {total_score:.2f}分 (未达到{thresholds['min_gap_score']:.1f}分阈值)")
        return "\n".join(report)


# ==============================================================================
# § C. 板块内部龙头结构分析模块 (移植自 dynamic_gap_detector.py)
# ==============================================================================

def find_concept_summary_file(concept_name, current_time, data_dir):
    """
    查找指定概念在指定时间的概念文件，兼容多种文件格式：
    1. concept_summary_{概念名称}_{日期}_{时间}.csv
    2. 概念_stocks_{概念名称}_{日期}_{时间}.csv
    支持模糊匹配和关键词匹配
    """
    try:
        all_files = os.listdir(data_dir)

        # 清理概念名称，去掉可能的"概念"后缀
        clean_concept_name = concept_name.replace('概念', '') if concept_name.endswith('概念') else concept_name

        # 查找匹配的概念文件，支持多种格式
        concept_files = []
        for f in all_files:
            if f.endswith('.csv'):
                # 精确匹配
                if (f.startswith(f'concept_summary_{concept_name}_') or
                    f.startswith(f'concept_summary_{clean_concept_name}_') or
                    f.startswith(f'概念_stocks_{concept_name}_') or
                    f.startswith(f'概念_stocks_{clean_concept_name}_')):
                    concept_files.append(f)
                # 模糊匹配：只进行合理的包含匹配，避免错误匹配
                elif (f.startswith('concept_summary_') or f.startswith('概念_stocks_')):
                    file_parts = f.split('_')
                    if len(file_parts) >= 3:
                        file_concept_name = file_parts[2]
                        # 只进行双向包含匹配，且要求匹配长度足够长以避免误匹配
                        if ((clean_concept_name in file_concept_name and len(clean_concept_name) >= 3) or
                            (file_concept_name in clean_concept_name and len(file_concept_name) >= 3)):
                            concept_files.append(f)

        if not concept_files:
            return None

        # 使用find_latest_file逻辑找到最新的文件
        return find_latest_file(concept_files, current_time)

    except Exception as e:
        print(f"查找概念文件失败: {e}")
        return None


def find_sector_summary_file(sector_name, current_time, data_dir):
    """
    查找指定板块在指定时间的行业文件，兼容多种文件格式：
    1. sector_summary_{行业名称}_{日期}_{时间}.csv
    2. 行业_stocks_{行业名称}_{日期}_{时间}.csv
    支持模糊匹配和关键词匹配
    """
    try:
        # 不使用文件缓存，确保每次都获取最新的文件列表，避免未来函数风险
        all_files = os.listdir(data_dir)

        # 查找匹配的行业文件，支持多种格式
        sector_files = []
        for f in all_files:
            if f.endswith('.csv'):
                # 精确匹配
                if (f.startswith(f'sector_summary_{sector_name}_') or
                    f.startswith(f'行业_stocks_{sector_name}_')):
                    sector_files.append(f)
                # 模糊匹配：只进行合理的包含匹配，避免错误匹配
                elif (f.startswith('sector_summary_') or f.startswith('行业_stocks_')):
                    file_parts = f.split('_')
                    if len(file_parts) >= 3:
                        file_sector_name = file_parts[2]
                        # 只进行双向包含匹配，且要求匹配长度足够长以避免误匹配
                        if ((sector_name in file_sector_name and len(sector_name) >= 3) or
                            (file_sector_name in sector_name and len(file_sector_name) >= 3)):
                            sector_files.append(f)

        if not sector_files:
            return None

        # 使用find_latest_file逻辑找到最新的文件
        return find_latest_file(sector_files, current_time)

    except Exception as e:
        print(f"查找行业文件失败: {e}")
        return None


def parse_internal_data(file_path):
    """解析板块/概念内部文件，返回排序后的正流入个股数据"""
    try:
        df = pd.read_csv(file_path, encoding='utf-8-sig')
        df['今日主力净流入-净额'] = df['今日主力净流入-净额'].apply(convert_to_float)
        positive_df = df[df['今日主力净流入-净额'] > 0].copy()
        return positive_df.sort_values(by='今日主力净流入-净额', ascending=False)
    except Exception:
        return None


def generate_internal_analysis_report(df_internal, sector_name, sector_file, sector_total_amount, current_time):
    """生成板块内部个股资金流分析报告（支持双龙头检测和占比判断）- 完整版"""
    try:
        # 取前10名用于分析
        top_stocks = df_internal.head(10)

        if len(top_stocks) < 2:
            return f"  \n--- {sector_name}内部个股分析 ---\n  数据不足，仅有{len(top_stocks)}只正流入个股"

        # 计算内部断层 - 增强版（支持双龙头检测+占比判断）
        inflows = top_stocks['今日主力净流入-净额'].tolist()
        names = top_stocks['名称'].tolist()

        # 尝试获取股票代码（如果数据中包含）
        codes = []
        if '代码' in top_stocks.columns:
            codes = top_stocks['代码'].tolist()
        else:
            # 简化版代码映射
            stock_name_to_code = {
                '科大讯飞': '002230', '寒武纪-U': '688256', '兆易创新': '603986',
                '阿石创': '300706', '宁波银行': '002142', '招商银行': '600036'
            }
            codes = [stock_name_to_code.get(name, '000000') for name in names]
        
        # 基础比例计算
        first_ratio = inflows[0] / inflows[1] if inflows[1] > 0 else 1.0
        first_amount = inflows[0] / 1e8  # 第1名资金量（亿）
        second_amount = inflows[1] / 1e8  # 第2名资金量（亿）
        first_second_gap = (inflows[0] - inflows[1]) / 1e8  # 第1-2名差距（亿）
        
        # 【新增】占比判断逻辑
        first_ratio_sector = (first_amount / sector_total_amount) * 100 if sector_total_amount > 0 else 0
        second_ratio_sector = (second_amount / sector_total_amount) * 100 if sector_total_amount > 0 else 0
        dual_ratio_sector = first_ratio_sector + second_ratio_sector
        
        # 如果有第3名，计算第2-3名关系
        has_third = len(inflows) >= 3 and inflows[2] > 0
        if has_third:
            second_third_ratio = inflows[1] / inflows[2]
            third_amount = inflows[2] / 1e8
            second_third_gap = (inflows[1] - inflows[2]) / 1e8
            third_ratio_sector = (third_amount / sector_total_amount) * 100 if sector_total_amount > 0 else 0
        else:
            second_third_ratio = float('inf')
            third_amount = 0
            second_third_gap = 0
            third_ratio_sector = 0
        
        # 结构类型判断 - 增强版判断逻辑
        structure_type, core_stocks, structure_detail = _determine_internal_structure(
            names, inflows, first_ratio, second_third_ratio if has_third else float('inf'),
            first_ratio_sector, second_ratio_sector, dual_ratio_sector, sector_total_amount
        )

        # 生成详细报告
        report_lines = [f"  \n--- {sector_name}内部个股分析 ---"]
        report_lines.append(f"  内部格局: 【{structure_type}】")
        
        if core_stocks:
            report_lines.append(f"  核心{sector_name}: {', '.join(core_stocks)}")
        
        # 添加关键指标
        report_lines.append(f"  前2股占{sector_name}比: {dual_ratio_sector:.1f}%")
        report_lines.append(f"  第1名vs第2名: {first_ratio:.2f}倍")
        
        if has_third:
            report_lines.append(f"  第2名vs第3名: {second_third_ratio:.2f}倍")
        
        # 【新增：核心概念/行业详细信息】
        if core_stocks:
            report_lines.append(f"  ★ 核心{sector_name}股票详情:")
            for i, stock_name in enumerate(core_stocks):
                if i < len(names) and i < len(inflows):
                    amount_yi = inflows[i] / 1e8
                    sector_pct = (amount_yi / sector_total_amount) * 100 if sector_total_amount > 0 else 0
                    code = codes[i] if i < len(codes) else '000000'
                    report_lines.append(f"    → {stock_name}({code}): {amount_yi:.2f}亿 ({sector_pct:.1f}%占{sector_name})")
        else:
            report_lines.append(f"  ★ {sector_name}资金分散，无明显核心股票")
        
        # 个股详情（前5名）
        report_lines.append("  个股详情:")
        for i, (name, inflow) in enumerate(zip(names[:5], inflows[:5])):
            amount_yi = inflow / 1e8
            sector_pct = (amount_yi / sector_total_amount) * 100 if sector_total_amount > 0 else 0
            code = codes[i] if i < len(codes) else '000000'
            report_lines.append(f"    {i+1}. {name}({code}): {amount_yi:.2f}亿 ({sector_pct:.1f}%)")

        return "\n".join(report_lines)
        
    except Exception as e:
        return f"  \n--- {sector_name}内部个股分析 ---\n  生成报告失败: {e}"

def _determine_internal_structure(names, inflows, first_ratio, second_third_ratio, 
                                first_ratio_sector, second_ratio_sector, dual_ratio_sector, sector_total_amount):
    """确定内部结构类型 - 参考dynamic_gap_detector.py的逻辑"""
    
    # 【优先级1】绝对龙头判断 - 基于板块占比的绝对优势
    if first_ratio_sector >= 50.0:
        return "绝对龙头", [names[0]], f"第1名占板块{first_ratio_sector:.1f}%"
    
    # 【优先级2】强势单股判断 - 基于比例+占比的双重条件
    if first_ratio_sector >= 40.0 and first_ratio >= 1.3:
        return "绝对龙头", [names[0]], f"强势单股（占比{first_ratio_sector:.1f}%, {first_ratio:.2f}倍优势）"
    
    # 【优先级3】三足鼎立检测 - 需要在双龙头检测之前
    if len(names) >= 3:
        third_ratio_sector = (inflows[2] / 1e8 / sector_total_amount) * 100 if sector_total_amount > 0 else 0
        triple_ratio_sector = first_ratio_sector + second_ratio_sector + third_ratio_sector
        
        # 三足鼎立条件（参考dynamic_gap_detector.py）
        triple_close = (first_ratio < 1.35 and second_third_ratio < 1.15)  # 内部接近
        triple_dominance = triple_ratio_sector >= 50.0  # 前三合占比足够
        third_significant = third_ratio_sector >= 12.0  # 第3名占比不能太小
        
        if triple_close and triple_dominance and third_significant:
            return "三足鼎立", names[:3], f"三强竞争（前3占{triple_ratio_sector:.1f}%）"
    
    # 【优先级4】双龙头检测 - 参考dynamic_gap_detector.py的完整逻辑
    if len(names) >= 2:
        # 双龙头检测条件（基于dynamic_gap_detector.py）
        # 条件1：前两名合计占比足够高
        dual_dominance = dual_ratio_sector >= 35.0
        
        # 条件2：个股占比平衡（避免一强一弱）  
        balanced_shares = (first_ratio_sector >= 15.0 and 
                         second_ratio_sector >= 12.0 and
                         first_ratio_sector < 35.0)
        
        # 条件3：前两名相对接近
        close_leaders = first_ratio < 1.40
        
        # 条件4：与第3名有明显差距（如果存在第3名）
        clear_separation = True
        if len(names) >= 3 and second_third_ratio != float('inf'):
            clear_separation = second_third_ratio >= 1.15
        
        # 综合判断双龙头
        if dual_dominance and balanced_shares and close_leaders and clear_separation:
            return "双龙头", names[:2], f"双强格局（合占{dual_ratio_sector:.1f}%）"
        
        # 高占比双龙头（放宽条件）
        elif (dual_ratio_sector >= 45.0 and first_ratio < 1.50 and 
              second_ratio_sector >= 15.0 and second_third_ratio >= 1.05):
            return "双龙头", names[:2], f"高占比双龙头（合占{dual_ratio_sector:.1f}%）"
    
    # 【优先级5】单股龙头判断
    if first_ratio_sector >= 25.0 and first_ratio >= 1.25:
        return "单股龙头", [names[0]], f"单股领先（占比{first_ratio_sector:.1f}%）"
    elif first_ratio >= 1.15 and (inflows[0] - inflows[1]) / 1e8 >= 0.1:
        return "单股龙头", [names[0]], f"单股领先（{first_ratio:.2f}倍优势）"
    
    # 【默认】竞争激烈
    return "竞争激烈", [], f"资金分散（第1名仅占{first_ratio_sector:.1f}%）"


# ==============================================================================
# § A2. 买入信号检测和过滤函数
# ==============================================================================

def check_concept_industry_rank(sector_name, sector_type, current_time, data_dir):
    """检查概念/行业是否在前N名内"""
    try:
        if sector_type == '概念':
            latest_file = find_latest_file([f for f in os.listdir(data_dir) if 'concept_fund_flow' in f and f.endswith('.csv')], current_time)
            max_rank = MAX_CONCEPT_RANK
        else:  # 行业
            latest_file = find_latest_file([f for f in os.listdir(data_dir) if 'sector_fund_flow' in f and f.endswith('.csv')], current_time)
            max_rank = MAX_INDUSTRY_RANK
        
        if not latest_file:
            return False, 999  # 找不到文件，假设排名很低
        
        df_sectors = pd.read_csv(os.path.join(data_dir, latest_file), encoding='utf-8-sig')
        # 统一列名
        df_sectors.rename(columns={'行业': '名称', '行业-涨跌幅': '今日涨跌幅', '净额': '今日主力净流入-净额'}, inplace=True)
        
        # 查找该概念/行业的排名
        if '名称' in df_sectors.columns:
            sector_rank = None
            for idx, row in df_sectors.iterrows():
                if row['名称'] == sector_name:
                    sector_rank = idx + 1
                    break
            
            if sector_rank and sector_rank <= max_rank:
                return True, sector_rank
            else:
                return False, sector_rank if sector_rank else 999
        
        return False, 999
        
    except Exception as e:
        print(f"检查{sector_type}排名失败: {e}")
        return False, 999


def get_current_sector_rank_from_analysis_context(sector_name, sector_type, current_analysis_data):
    """从当前分析上下文中获取板块排名（更准确的方法）"""
    try:
        if current_analysis_data is not None and not current_analysis_data.empty:
            # 查找该板块在当前分析数据中的排名
            for idx, row in current_analysis_data.iterrows():
                if row['名称'] == sector_name:
                    rank = idx + 1
                    max_rank = MAX_CONCEPT_RANK if sector_type == '概念' else MAX_INDUSTRY_RANK
                    is_top_rank = rank <= max_rank
                    return is_top_rank, rank
        
        return False, 999
    except Exception as e:
        print(f"从分析上下文获取排名失败: {e}")
        return False, 999


def detect_buy_signals_from_analysis(sector_name, structure_type, core_stocks, current_time, data_dir, backtester, sector_rank=None, sector_internal_data=None):
    """从板块分析结果中检测买入信号"""
    buy_signals = []

    # 检查是否启用该信号类型
    if not backtester.is_signal_type_enabled(structure_type):
        return buy_signals

    # 使用传入的排名或者重新检查排名
    if sector_rank is not None:
        max_rank = MAX_CONCEPT_RANK if '概念' in str(sector_name) else MAX_INDUSTRY_RANK
        is_top_rank = sector_rank <= max_rank
        rank = sector_rank
    else:
        # 检查概念/行业排名是否符合要求
        sector_type = '概念' if '概念' in str(sector_name) else '行业'
        is_top_rank, rank = check_concept_industry_rank(sector_name, sector_type, current_time, data_dir)

    if not is_top_rank:
        print(f"⚠️ {sector_name}排名第{rank}名，不在前{MAX_CONCEPT_RANK if '概念' in str(sector_name) else MAX_INDUSTRY_RANK}名内，跳过买入")
        return buy_signals

    # 根据信号类型生成买入信号
    if structure_type in ['绝对龙头', '单股龙头'] and core_stocks:
        # 绝对龙头和单股龙头：买入第1名
        stock_name = core_stocks[0]
        buy_signals.append({
            'stock_name': stock_name,
            'signal_type': structure_type,
            'sector_name': sector_name,
            'sector_rank': rank,
            'reason': f"{sector_name}第{rank}名-{structure_type}",
            'sector_internal_data': sector_internal_data  # 传递板块内部数据
        })

    elif structure_type == '双龙头' and len(core_stocks) >= 2:
        # 双龙头：买入前2名
        for i in range(2):
            if i < len(core_stocks):
                stock_name = core_stocks[i]
                buy_signals.append({
                    'stock_name': stock_name,
                    'signal_type': structure_type,
                    'sector_name': sector_name,
                    'sector_rank': rank,
                    'reason': f"{sector_name}第{rank}名-{structure_type}第{i+1}股",
                    'sector_internal_data': sector_internal_data  # 传递板块内部数据
                })

    elif structure_type == '三足鼎立' and len(core_stocks) >= 3:
        # 三足鼎立：买入前3名
        for i in range(3):
            if i < len(core_stocks):
                stock_name = core_stocks[i]
                buy_signals.append({
                    'stock_name': stock_name,
                    'signal_type': structure_type,
                    'sector_name': sector_name,
                    'sector_rank': rank,
                    'reason': f"{sector_name}第{rank}名-{structure_type}第{i+1}股",
                    'sector_internal_data': sector_internal_data  # 传递板块内部数据
                })

    return buy_signals


def execute_buy_signals(buy_signals, stock_price_data, current_time, backtester):
    """执行买入信号"""
    successful_buys = 0
    
    # 调试：打印可用的股票名称和价格信息
    if not stock_price_data.empty:
        print(f"[DEBUG] 价格数据中可用股票数量: {len(stock_price_data)}")
        print(f"[DEBUG] 价格数据中前5只股票: {stock_price_data['名称'].head(5).tolist()}")
        if '最新价' in stock_price_data.columns:
            print(f"[DEBUG] 价格数据中是否包含最新价列: 是")
            print(f"[DEBUG] 前5只股票的价格: {stock_price_data['最新价'].head(5).tolist()}")
            valid_prices = stock_price_data[stock_price_data['最新价'] > 0]
            print(f"[DEBUG] 有效价格数据数量: {len(valid_prices)}")
            if len(valid_prices) > 0:
                print(f"[DEBUG] 有效价格样例: {valid_prices[['名称', '最新价']].head(3).to_dict('records')}")
        else:
            print(f"[DEBUG] 价格数据中不包含最新价列，可用列: {list(stock_price_data.columns)}")
    
    for signal in buy_signals:
        stock_name = signal['stock_name']
        signal_type = signal['signal_type']

        # 优先从板块内部数据中查找价格信息
        stock_code = None
        stock_price = 0

        # 1. 首先尝试从板块内部数据获取价格（如果有的话）
        if 'sector_internal_data' in signal and signal['sector_internal_data'] is not None:
            sector_data = signal['sector_internal_data']
            if not sector_data.empty:
                sector_matching = sector_data[sector_data['名称'] == stock_name]
                if not sector_matching.empty:
                    sector_row = sector_matching.iloc[0]
                    if '最新价' in sector_row and pd.notna(sector_row['最新价']) and sector_row['最新价'] > 0:
                        stock_price = float(sector_row['最新价'])
                        stock_code = sector_row.get('代码', f"UNKNOWN_{stock_name}")
                        print(f"[INFO] 从板块内部数据获取 {stock_name} 价格: {stock_price}")

        # 2. 如果板块内部数据没有有效价格，再从主要价格数据中查找
        if stock_price <= 0:
            matching_stocks = stock_price_data[stock_price_data['名称'] == stock_name]
            if matching_stocks.empty:
                print(f"⚠️ 未找到股票 {stock_name} 的价格数据")
                # 查找相似的股票名称
                similar_names = []
                for name in stock_price_data['名称'].dropna():
                    if stock_name in str(name) or str(name) in stock_name:
                        similar_names.append(name)
                if similar_names:
                    print(f"[DEBUG] 发现相似股票名称: {similar_names[:3]}")
                continue

            stock_row = matching_stocks.iloc[0]
            stock_code = stock_row.get('代码', f"UNKNOWN_{stock_name}")
            stock_price = stock_row.get('最新价', 0)

        # 3. 最终价格检查
        if stock_price <= 0:
            print(f"⚠️ 股票 {stock_name} 价格异常: {stock_price}")
            continue
        
        # 执行买入
        success = backtester.buy_by_signal_type(
            stock_code=stock_code,
            stock_name=stock_name,
            price=stock_price,
            buy_time=current_time,
            signal_type=signal_type
        )
        
        if success:
            successful_buys += 1
            print(f"📈 买入信号执行成功: {signal['reason']}")
    
    return successful_buys


def analyze_sector_internal_flow_wrapper(leader_name, sector_type, sector_total_amount, current_time, data_dir, enable_buy_signals=False, backtester=None):
    """板块内部龙头结构分析的包装函数，支持买入信号检测"""
    internal_report = f"\n--- {leader_name} ({sector_type}) 内部个股分析 ---"
    buy_signals = []

    # 根据板块类型选择相应的文件查找函数
    if sector_type == '概念':
        file_path = find_concept_summary_file(leader_name, current_time, data_dir)
        if not file_path:
            internal_report += f"\n  未找到{leader_name}概念文件"
            return internal_report, buy_signals
    else:  # 行业
        file_path = find_sector_summary_file(leader_name, current_time, data_dir)
        if not file_path:
            internal_report += f"\n  未找到对应的内部资金流数据文件"
            return internal_report, buy_signals

    df_internal = parse_internal_data(os.path.join(data_dir, file_path))

    if df_internal is None or df_internal.empty:
        internal_report += "\n  无正流入个股数据"
        return internal_report, buy_signals

    internal_report = generate_internal_analysis_report(df_internal, leader_name, file_path, sector_total_amount, current_time)

    # 打印Top 5个股列表
    top5_internal = df_internal.head(5)
    table_data = []
    for i, (_, row) in enumerate(top5_internal.iterrows(), 1):
        table_data.append([i, row['名称'], format_amount(row['今日主力净流入-净额'])])

    internal_report += "\n" + tabulate(table_data, headers=['排名', '股票名称', '净流入'], tablefmt='psql',
                                       showindex=False)

    # 【新增】买入信号检测
    if enable_buy_signals and backtester:
        try:
            # 从内部分析报告中提取结构类型和核心股票
            lines = internal_report.split('\n')
            structure_type = None
            core_stocks = []
            
            for line in lines:
                if '内部格局:' in line:
                    # 提取结构类型，例如 "内部格局: 【双龙头】"
                    start = line.find('【') + 1
                    end = line.find('】')
                    if start > 0 and end > start:
                        structure_type = line[start:end]
                elif '核心' in line and sector_type in line and '→' in line:
                    # 提取核心股票名称，例如 "→ 科大讯飞(002230): 1.23亿"
                    stock_part = line.split('→')[1].strip() if '→' in line else ''
                    if '(' in stock_part:
                        stock_name = stock_part.split('(')[0].strip()
                        if stock_name:
                            core_stocks.append(stock_name)
            
            # 如果从报告中提取不到，直接从数据中获取
            if not core_stocks and not top5_internal.empty:
                core_stocks = top5_internal['名称'].head(5).tolist()
            
            if structure_type and core_stocks:
                buy_signals = detect_buy_signals_from_analysis(
                    leader_name, structure_type, core_stocks, current_time, data_dir, backtester,
                    sector_rank=None,  # 将由函数内部重新检查
                    sector_internal_data=df_internal  # 传递板块内部数据
                )
                
                if buy_signals:
                    internal_report += f"\n🎯 检测到{len(buy_signals)}个买入信号:"
                    for signal in buy_signals:
                        internal_report += f"\n  - {signal['reason']}: {signal['stock_name']}"
                        
        except Exception as e:
            print(f"买入信号检测失败: {e}")

    return internal_report, buy_signals


# ==============================================================================
# § D. 涨停股池分析模块 (移植自 dynamic_gap_detector.py)
# ==============================================================================

def parse_limit_up_pool_data(file_path):
    """解析涨停股池文件，返回涨停股票信息"""
    try:
        df = pd.read_csv(file_path, encoding='utf-8-sig', on_bad_lines='skip', low_memory=False)
        # 查找可能的列名
        name_columns = ['名称', 'name', '股票名称', '证券名称']
        code_columns = ['代码', 'code', '股票代码', '证券代码'] 
        price_columns = ['最新价', 'price', '现价', '股价']
        # 新增列名映射
        additional_columns = {
            '成交额': ['成交额', 'volume', '成交量'],
            '封板资金': ['封板资金', 'seal_amount', '封单金额'], 
            '首次封板时间': ['首次封板时间', 'first_seal_time', '首封时间'],
            '最后封板时间': ['最后封板时间', 'last_seal_time', '末封时间'],
            '炸板次数': ['炸板次数', 'break_count', '开板次数'],
            '涨停统计': ['涨停统计', 'limit_up_stat', '涨停次数'],
            '连板数': ['连板数', 'consecutive_days', '连续涨停'],
            '所属行业': ['所属行业', 'industry', '行业', '板块']
        }
        
        name_col = next((col for col in name_columns if col in df.columns), None)
        code_col = next((col for col in code_columns if col in df.columns), None) 
        price_col = next((col for col in price_columns if col in df.columns), None)
        
        if name_col:
            result = []
            for _, row in df.iterrows():
                stock_info = {'名称': row[name_col]}
                if code_col and pd.notna(row[code_col]):
                    stock_info['代码'] = row[code_col]
                if price_col and pd.notna(row[price_col]):
                    stock_info['最新价'] = row[price_col]
                # 添加新的字段
                for target_col, possible_names in additional_columns.items():
                    for possible_name in possible_names:
                        if possible_name in df.columns and pd.notna(row[possible_name]):
                            stock_info[target_col] = row[possible_name]
                            break
                result.append(stock_info)
            return result
        return []
    except Exception as e:
        print(f"解析涨停股池文件失败: {e}")
        return []


def extract_limit_up_key_sectors(current_limit_up_stocks):
    """提取涨停池中的关键行业和概念，返回需要进行内部分析的行业和概念列表"""
    if not current_limit_up_stocks:
        return [], []
    
    # 统计涨停行业分布
    industry_count = {}
    concept_count = {}
    
    for stock in current_limit_up_stocks:
        industry = stock.get('所属行业', '未知')
        if industry != '未知':
            industry_count[industry] = industry_count.get(industry, 0) + 1
        
        # 获取概念信息
        stock_name = stock.get('名称', '')
        sectors_info = get_stock_sectors(stock_name)
        if sectors_info and sectors_info.get('concepts'):
            for concept in sectors_info.get('concepts', [])[:3]:  # 只取前3个概念
                concept_count[concept] = concept_count.get(concept, 0) + 1

    # 统计连板数分布
    consecutive_stats = {}
    consecutive_concept_stats = {}
    
    for stock in current_limit_up_stocks:
        consecutive_days = stock.get('连板数', 1)
        try:
            consecutive_days = int(consecutive_days) if pd.notna(consecutive_days) else 1
        except:
            consecutive_days = 1
            
        industry = stock.get('所属行业', '未知')
        
        if consecutive_days not in consecutive_stats:
            consecutive_stats[consecutive_days] = {}
            consecutive_concept_stats[consecutive_days] = {}
        
        if industry != '未知':
            consecutive_stats[consecutive_days][industry] = consecutive_stats[consecutive_days].get(industry, 0) + 1
        
        # 概念统计
        stock_name = stock.get('名称', '')
        sectors_info = get_stock_sectors(stock_name)
        if sectors_info and sectors_info.get('concepts'):
            for concept in sectors_info.get('concepts', [])[:3]:
                consecutive_concept_stats[consecutive_days][concept] = consecutive_concept_stats[consecutive_days].get(concept, 0) + 1

    # 收集关键行业和概念
    key_industries = set()
    key_concepts = set()
    
    # 1. 涨停行业最多的
    if industry_count:
        max_industry = max(industry_count.items(), key=lambda x: x[1])
        if max_industry[1] >= 2:  # 至少2个涨停股
            key_industries.add(max_industry[0])
    
    # 2. 从连板统计中提取关键行业
    if consecutive_stats:
        max_consecutive = max(consecutive_stats.keys())
        
        # 连板数最多行业
        if max_consecutive > 1 and max_consecutive in consecutive_stats:
            max_consecutive_industry = max(consecutive_stats[max_consecutive].items(), key=lambda x: x[1])
            if max_consecutive_industry[1] >= 2:
                key_industries.add(max_consecutive_industry[0])
        
        # 首板最多行业
        if 1 in consecutive_stats:
            max_first_board_industry = max(consecutive_stats[1].items(), key=lambda x: x[1])
            if max_first_board_industry[1] >= 2:
                key_industries.add(max_first_board_industry[0])
        
        # 2板、3板等最多的行业
        for consecutive_days in sorted(consecutive_stats.keys()):
            if consecutive_days > 1 and consecutive_days < max_consecutive:
                max_industry = max(consecutive_stats[consecutive_days].items(), key=lambda x: x[1])
                if max_industry[1] >= 2:
                    key_industries.add(max_industry[0])

    # 3. 从概念统计中提取关键概念
    if concept_count:
        # 获取概念数量最多的前3个
        top_concepts = sorted(concept_count.items(), key=lambda x: x[1], reverse=True)[:3]
        for concept, count in top_concepts:
            if count >= 2:  # 至少2个涨停股
                key_concepts.add(concept)
    
    # 4. 从连板概念统计中提取关键概念
    for consecutive_days, concept_stats in consecutive_concept_stats.items():
        if concept_stats:
            top_concepts = sorted(concept_stats.items(), key=lambda x: x[1], reverse=True)[:2]
            for concept, count in top_concepts:
                if count >= 2:
                    key_concepts.add(concept)
    
    return list(key_industries), list(key_concepts)


def display_limit_up_changes_and_stats(current_limit_up_stocks, previous_limit_up_stocks):
    """显示涨停变化和统计信息"""
    # 显示新增和退出涨停股
    if previous_limit_up_stocks:
        current_names = {stock.get('名称', '') for stock in current_limit_up_stocks}
        previous_names = {stock.get('名称', '') for stock in previous_limit_up_stocks}
        
        new_limit_up = [name for name in current_names if name not in previous_names]
        removed_limit_up = [name for name in previous_names if name not in current_names]
        
        if new_limit_up:
            print(f"[*] 新增涨停: {', '.join(new_limit_up)}")
        if removed_limit_up:
            print(f"[COLD] 退出涨停: {', '.join(removed_limit_up)}")
    
    # 统计涨停行业分布
    industry_count = {}
    concept_count = {}
    
    for stock in current_limit_up_stocks:
        industry = stock.get('所属行业', '未知')
        industry_count[industry] = industry_count.get(industry, 0) + 1
        
        # 获取概念信息
        stock_name = stock.get('名称', '')
        sectors_info = get_stock_sectors(stock_name)
        if sectors_info and sectors_info.get('concepts'):
            for concept in sectors_info.get('concepts', [])[:3]:  # 只取前3个概念
                concept_count[concept] = concept_count.get(concept, 0) + 1

    # 涨停行业最多的
    if industry_count:
        max_industry = max(industry_count.items(), key=lambda x: x[1])
        # 找出概念中数量最多的
        concept_display = ""
        if concept_count:
            top_concepts = sorted(concept_count.items(), key=lambda x: x[1], reverse=True)[:3]
            if top_concepts:
                max_count = top_concepts[0][1]
                max_concepts = [f"{concept}{count}个" for concept, count in top_concepts if count == max_count and count >= 2]
                concept_display = f"    |   {' | '.join(max_concepts)}|" if max_concepts else ""
        print(f"涨停行业最多的：{max_industry[0]}  {max_industry[1]}个{concept_display}")

    # 统计连板数分布
    consecutive_stats = {}
    consecutive_concept_stats = {}
    
    for stock in current_limit_up_stocks:
        consecutive_days = stock.get('连板数', 1)
        try:
            consecutive_days = int(consecutive_days) if pd.notna(consecutive_days) else 1
        except:
            consecutive_days = 1
            
        industry = stock.get('所属行业', '未知')
        
        if consecutive_days not in consecutive_stats:
            consecutive_stats[consecutive_days] = {}
            consecutive_concept_stats[consecutive_days] = {}
        
        consecutive_stats[consecutive_days][industry] = consecutive_stats[consecutive_days].get(industry, 0) + 1
        
        # 概念统计
        stock_name = stock.get('名称', '')
        sectors_info = get_stock_sectors(stock_name)
        if sectors_info and sectors_info.get('concepts'):
            for concept in sectors_info.get('concepts', [])[:3]:
                consecutive_concept_stats[consecutive_days][concept] = consecutive_concept_stats[consecutive_days].get(concept, 0) + 1

    if consecutive_stats:
        # 连板数最多行业
        max_consecutive = max(consecutive_stats.keys())
        if max_consecutive > 1:
            max_consecutive_industry = max(consecutive_stats[max_consecutive].items(), key=lambda x: x[1])
            concept_display = ""
            if max_consecutive in consecutive_concept_stats:
                top_concepts = sorted(consecutive_concept_stats[max_consecutive].items(), key=lambda x: x[1], reverse=True)[:3]
                if top_concepts:
                    max_count = top_concepts[0][1]
                    max_concepts = [f"{concept}{count}个" for concept, count in top_concepts if count == max_count and count >= 2]
                    concept_display = f"    |   {' | '.join(max_concepts)}|" if max_concepts else ""
            print(f"连板数最多行业：{max_consecutive_industry[0]}  {max_consecutive_industry[1]}个  {max_consecutive}连扳{concept_display}")

        # 首扳最多行业
        if 1 in consecutive_stats:
            max_first_board_industry = max(consecutive_stats[1].items(), key=lambda x: x[1])
            concept_display = ""
            if 1 in consecutive_concept_stats:
                top_concepts = sorted(consecutive_concept_stats[1].items(), key=lambda x: x[1], reverse=True)[:3]
                if top_concepts:
                    max_count = top_concepts[0][1]
                    max_concepts = [f"{concept}{count}个" for concept, count in top_concepts if count == max_count and count >= 2]
                    concept_display = f"    |   {' | '.join(max_concepts)}|" if max_concepts else ""
            print(f"首扳最多行业：{max_first_board_industry[0]}  {max_first_board_industry[1]}个  首扳{concept_display}")

        # 2板、3板等最多的行业
        for consecutive_days in sorted(consecutive_stats.keys()):
            if consecutive_days > 1 and consecutive_days < max_consecutive:
                max_industry = max(consecutive_stats[consecutive_days].items(), key=lambda x: x[1])
                concept_display = ""
                if consecutive_days in consecutive_concept_stats:
                    top_concepts = sorted(consecutive_concept_stats[consecutive_days].items(), key=lambda x: x[1], reverse=True)[:3]
                    if top_concepts:
                        max_count = top_concepts[0][1]
                        max_concepts = [f"{concept}{count}个" for concept, count in top_concepts if count == max_count and count >= 2]
                        concept_display = f"    |   {' | '.join(max_concepts)}|" if max_concepts else ""
                print(f"{consecutive_days}板连板数最多行业：{max_industry[0]}  {max_industry[1]}个  {consecutive_days}连扳{concept_display}")


def display_limit_up_pool_table(current_limit_up_stocks, stock_flow_data=None):
    """显示涨停股池表格"""
    if not current_limit_up_stocks:
        print("暂无涨停股池数据。")
        return
        
    display_limit_up = current_limit_up_stocks[:20]  # 显示前20只
    
    # 如果涨停股池数据中没有资金流入信息，尝试从个股资金流数据中匹配
    if stock_flow_data is not None and not stock_flow_data.empty:
        for i, stock in enumerate(display_limit_up):
            stock_name = stock.get('名称', '')
            matching_flow = stock_flow_data[stock_flow_data['名称'] == stock_name]
            if not matching_flow.empty:
                flow_row = matching_flow.iloc[0]
                display_limit_up[i]['今日主力净流入-净额'] = flow_row.get('今日主力净流入-净额', 0)
                display_limit_up[i]['今日主力净流入-净占比'] = flow_row.get('今日主力净流入-净占比', 0)
                if '代码' not in display_limit_up[i] and '代码' in flow_row:
                    display_limit_up[i]['代码'] = flow_row['代码']

    limit_up_data = []
    for i, stock in enumerate(display_limit_up, 1):
        row = [i, stock['名称']]
        
        # 基础信息
        if '代码' in stock and pd.notna(stock['代码']):
            row.append(stock['代码'])
        else:
            row.append('N/A')
            
        if '最新价' in stock:
            row.append(f"{stock['最新价']:.2f}" if pd.notna(stock['最新价']) else 'N/A')
        else:
            row.append('N/A')
            
        # 成交额
        if '成交额' in stock:
            amount = stock['成交额']
            if pd.notna(amount) and amount > 0:
                row.append(f"{amount/1e8:.2f}亿" if amount >= 1e8 else f"{amount/1e4:.0f}万")
            else:
                row.append('N/A')
        else:
            row.append('N/A')
            
        # 封板资金
        if '封板资金' in stock:
            seal_amount = stock['封板资金']
            if pd.notna(seal_amount) and seal_amount > 0:
                row.append(f"{seal_amount/1e8:.2f}亿" if seal_amount >= 1e8 else f"{seal_amount/1e4:.0f}万")
            else:
                row.append('N/A')
        else:
            row.append('N/A')
            
        # 时间信息
        if '首次封板时间' in stock:
            row.append(str(stock['首次封板时间']) if pd.notna(stock['首次封板时间']) else 'N/A')
        else:
            row.append('N/A')
            
        if '最后封板时间' in stock:
            row.append(str(stock['最后封板时间']) if pd.notna(stock['最后封板时间']) else 'N/A')
        else:
            row.append('N/A')
            
        # 其他信息
        if '炸板次数' in stock:
            row.append(str(int(stock['炸板次数'])) if pd.notna(stock['炸板次数']) else '0')
        else:
            row.append('0')
            
        if '涨停统计' in stock:
            row.append(str(stock['涨停统计']) if pd.notna(stock['涨停统计']) else 'N/A')
        else:
            row.append('N/A')
            
        if '连板数' in stock:
            row.append(str(int(stock['连板数'])) if pd.notna(stock['连板数']) else '1')
        else:
            row.append('1')
            
        if '所属行业' in stock:
            row.append(str(stock['所属行业']) if pd.notna(stock['所属行业']) else 'N/A')
        else:
            row.append('N/A')
            
        # 添加资金流入信息（如果有）
        if '今日主力净流入-净额' in stock:
            inflow = stock['今日主力净流入-净额']
            if pd.notna(inflow) and inflow > 0:
                row.append('√')
            else:
                row.append('')
        else:
            row.append('')
            
        # 风险警示（简化版）
        row.append('')
        
        limit_up_data.append(row)

    # 构建表头
    headers = ['排名', '股票名称', '股票代码', '最新价', '成交额', '封板资金', 
               '首次封板时间', '最后封板时间', '炸板次数', '涨停统计', '连板数', 
               '所属行业', '大于历史资金流入', '风险警示']
    
    # 设置最大列宽
    max_col_widths = [6, 10, 8, 8, 8, 8, 12, 12, 8, 8, 8, 10, 12, 10]
    print(tabulate(limit_up_data, headers=headers, tablefmt='psql', maxcolwidths=max_col_widths))


# ==============================================================================
# § E. 回测主函数 (基于 backtestv6.py 改造)
# ==============================================================================

def run_analysis_backtest(date_str):
    """
    主回测分析函数，集成个股断层和板块内部结构分析。
    新增：回测买入信号功能
    """
    print(f"--- 开始对日期 {date_str} 进行盘后复盘 (V8.0 回测买入信号版) ---")
    data_dir = os.path.join(BASE_DATA_DIR, date_str)
    if not os.path.isdir(data_dir):
        print(f"错误: 目录不存在 {data_dir}")
        return

    # 初始化回测引擎
    backtester = BacktestEngine()
    print(f"📊 回测引擎初始化完成 - 总资金: {backtester.total_capital:,}")
    
    # 记录今日所有买入信号
    daily_buy_signals = []

    # 1. 准备数据文件列表
    all_files = os.listdir(data_dir)
    
    # 扩展个股资金流文件匹配模式（参考dynamic_gap_detector.py）
    stock_flow_patterns = [
        'fund_flow_rank_',        # 个股资金流排名
        'fund_flow_tpdog.csv',    # tpdog个股资金流
        'ths_fund_flow.csv',      # 同花顺个股资金流
        'fund_flow_akshare.csv',  # akshare个股资金流
        'individual_fund_flow_',  # 个股资金流排名
        '股票资金流_zssz_',        # 深圳个股资金流
        '股票资金流_zssh_',        # 上海个股资金流
        '个股资金流_',            # 个股资金流
        'stock_fund_flow_'        # 个股资金流通用
    ]
    
    rank_files = []
    for file in all_files:
        if file.endswith('.csv'):
            # 排除概念和行业文件
            if 'concept_fund_flow' in file or 'sector_fund_flow' in file:
                continue
            for pattern in stock_flow_patterns:
                if pattern in file:
                    rank_files.append(file)
                    break
    
    rank_files = sorted(rank_files)
    industry_files = sorted([f for f in all_files if 'sector_fund_flow' in f and f.endswith('.csv')])
    concept_files = sorted([f for f in all_files if 'concept_fund_flow' in f and f.endswith('.csv')])
    
    # 添加涨停股池文件匹配（参考dynamic_gap_detector.py）
    limit_up_patterns = [
        'limit_up_pool_',         # 涨停股池
        'zt_pool_',              # 涨停池（新增：支持zt_pool_YYYYMMDD_HHMMSS.csv格式）
        'zt_pool.csv',           # 涨停池
        '涨停股池_',              # 涨停股池中文
        'limit_up_'              # 涨停相关
    ]
    
    limit_up_files = []
    for file in all_files:
        if file.endswith('.csv'):
            for pattern in limit_up_patterns:
                if pattern in file:
                    limit_up_files.append(file)
                    break
    limit_up_files = sorted(limit_up_files)

    if not rank_files:
        print("错误: 未找到任何个股资金流数据文件！")
        print(f"可用文件: {all_files[:10] if len(all_files) > 10 else all_files}")
        return
    
    print(f"[DEBUG] 找到个股资金流文件 {len(rank_files)} 个: {rank_files[:3]}...")
    print(f"[DEBUG] 找到概念文件 {len(concept_files)} 个: {concept_files[:3]}...")
    print(f"[DEBUG] 找到行业文件 {len(industry_files)} 个: {industry_files[:3]}...")
    print(f"[DEBUG] 找到涨停股池文件 {len(limit_up_files)} 个: {limit_up_files[:3]}...")

    all_timestamps = sorted(
        list(set(extract_timestamp_from_filename(f) for f in rank_files if extract_timestamp_from_filename(f))))

    # 2. 循环模拟每个时间点
    for ts_str in all_timestamps:
        current_sim_time_obj = datetime.strptime(ts_str, '%H%M%S').time()
        # 创建完整的datetime对象用于时间运算
        current_sim_time_full = datetime.combine(datetime.today(), current_sim_time_obj)

        # 只分析交易时间段
        if not (time(9, 30) <= current_sim_time_obj <= time(11, 30) or time(13, 0) <= current_sim_time_obj <= time(15, 0)):
            continue

        print("\n" + "=" * 25 + f" 模拟时间点: {current_sim_time_obj} " + "=" * 25)

        # 3. 加载当前时间点的所有数据
        latest_rank_file = find_latest_file(rank_files, current_sim_time_obj)
        latest_industry_file = find_latest_file(industry_files, current_sim_time_obj)
        latest_concept_file = find_latest_file(concept_files, current_sim_time_obj)
        latest_limit_up_file = find_latest_file(limit_up_files, current_sim_time_obj)

        # 3.1 加载涨停股池数据
        current_limit_up_stocks = []
        if latest_limit_up_file:
            print(f"[DEBUG] 读取涨停股池文件: {latest_limit_up_file}")
            current_limit_up_stocks = parse_limit_up_pool_data(os.path.join(data_dir, latest_limit_up_file))
            print(f"[DEBUG] 涨停股池数量: {len(current_limit_up_stocks)}")

        # 4. 执行个股资金流断层分析
        if latest_rank_file:
            print(f"[DEBUG] 读取个股文件: {latest_rank_file}")
            try:
                df_stocks = pd.read_csv(os.path.join(data_dir, latest_rank_file), encoding='utf-8-sig')
                df_stocks.columns = df_stocks.columns.str.strip().str.replace('\ufeff', '')
                print(f"[DEBUG] 原始文件列名: {list(df_stocks.columns)}")
                print(f"[DEBUG] 数据行数: {len(df_stocks)}")
                
                # 应用列名标准化
                file_format = classify_file_type(latest_rank_file)
                print(f"[DEBUG] 识别文件格式: {file_format}")
                df_stocks = standardize_stock_data(df_stocks, file_format)
                
                if df_stocks is None:
                    print("[ERROR] 列名标准化失败")
                    continue
                    
                print(f"[DEBUG] 标准化后列名: {list(df_stocks.columns)}")
                if not df_stocks.empty:
                    print(f"[DEBUG] 前3行股票名称: {df_stocks['名称'].head(3).tolist()}")
                    if '今日主力净流入-净额' in df_stocks.columns:
                        print(f"[DEBUG] 原始净流入数据前3行: {df_stocks['今日主力净流入-净额'].head(3).tolist()}")
                    if '最新价' in df_stocks.columns:
                        print(f"[DEBUG] 原始价格数据前10行: {df_stocks[['名称', '最新价']].head(10).to_dict('records')}")
                        # 特别检查银行股的价格数据
                        bank_stocks = df_stocks[df_stocks['名称'].str.contains('银行', na=False)]
                        if not bank_stocks.empty:
                            print(f"[DEBUG] 银行股价格数据: {bank_stocks[['名称', '最新价']].to_dict('records')}")
                
                # 使用convert_to_float处理monetary数据（支持中文单位"万"、"亿"）
                if '今日主力净流入-净额' in df_stocks.columns:
                    df_stocks['今日主力净流入-净额'] = df_stocks['今日主力净流入-净额'].apply(convert_to_float)
                    print(f"[DEBUG] 转换后净流入数据前3行: {df_stocks['今日主力净流入-净额'].head(3).tolist()}")
                
                # 使用pd.to_numeric处理纯数字列
                for col in ['今日涨跌幅', '最新价']:
                    if col in df_stocks.columns:
                        print(f"[DEBUG] 处理{col}列前的数据样例: {df_stocks[col].head(3).tolist()}")
                        # 特别处理最新价列，保持原值避免错误转换
                        if col == '最新价':
                            print(f"[DEBUG] 价格列原始数据类型: {df_stocks[col].dtype}")
                            print(f"[DEBUG] 价格列是否全为空: {df_stocks[col].isna().all()}")
                            print(f"[DEBUG] 价格列非空值数量: {df_stocks[col].notna().sum()}")
                            
                            # 检查原始数据格式
                            non_null_prices = df_stocks[col].dropna()
                            if len(non_null_prices) > 0:
                                print(f"[DEBUG] 非空价格值样例: {non_null_prices.head(5).tolist()}")
                                print(f"[DEBUG] 非空价格值类型: {[type(x) for x in non_null_prices.head(3)]}")
                            
                            # 如果原始数据就是空的或无效的，跳过处理
                            if df_stocks[col].isna().all() or (df_stocks[col] == '').all():
                                print(f"[DEBUG] {col}列数据为空，跳过处理")
                                continue
                            
                            # 尝试直接转换数字
                            df_stocks[col] = pd.to_numeric(df_stocks[col], errors='coerce')
                        else:
                            df_stocks[col] = pd.to_numeric(df_stocks[col], errors='coerce')
                        print(f"[DEBUG] 处理{col}列后的数据样例: {df_stocks[col].head(3).tolist()}")
                
                # 特别处理净占比列（可能包含%符号）
                if '今日主力净流入-净占比' in df_stocks.columns:
                    df_stocks['今日主力净流入-净占比'] = df_stocks['今日主力净流入-净占比'].apply(convert_to_float)

                # 调用个股断层分析并打印结果
                stock_gap_report = analyze_stock_flow_gap(df_stocks, current_time=current_sim_time_full, data_dir=data_dir, file_format=file_format)
                if stock_gap_report:
                    print(stock_gap_report)

                # 打印个股资金流排行榜（使用与dynamic_gap_detector.py相同的显示逻辑）
                print(f"\n--- 个股资金流入 Top 20 (模拟时间点: {current_sim_time_obj}) ---")
                
                # 按主力净流入排序（从大到小）
                positive_stocks = df_stocks[df_stocks['今日主力净流入-净额'] > 0].copy()
                if positive_stocks.empty:
                    print("  没有资金净流入的股票")
                    continue
                    
                display_stocks = positive_stocks.head(20).copy()
                
                # 构建显示列和表头（与dynamic_gap_detector.py保持一致）
                display_columns = ['排名', '名称']
                headers = ['排名', '股票名称']
                
                # 添加排名列
                display_stocks['排名'] = range(1, len(display_stocks) + 1)
                
                # 【关键修复】添加股票代码列（如果存在）
                if '代码' in display_stocks.columns:
                    display_columns.append('代码')
                    headers.append('股票代码')
                
                # 【关键修复】添加最新价列（如果存在）
                if '最新价' in display_stocks.columns:
                    # 检查是否有有效的价格数据
                    valid_prices = display_stocks['最新价'][display_stocks['最新价'] > 0]
                    if len(valid_prices) > 0:
                        display_stocks['价格'] = display_stocks['最新价'].apply(
                            lambda x: f"{x:.2f}" if pd.notna(x) and x > 0 else 'N/A')
                        display_columns.append('价格')
                        headers.append('最新价')
                
                # 处理涨跌幅列
                if '今日涨跌幅' in display_stocks.columns:
                    # 检查是否有非零涨跌幅
                    non_zero_changes = display_stocks['今日涨跌幅'][display_stocks['今日涨跌幅'] != 0]
                    if len(non_zero_changes) > 0:
                        display_stocks['涨跌幅'] = display_stocks['今日涨跌幅'].apply(
                            lambda x: f"{x:.2f}%" if pd.notna(x) else 'N/A')
                        display_columns.append('涨跌幅')
                        headers.append('今日涨跌幅')
                
                # 添加主力净流入列
                display_stocks['主力净流入-净额'] = display_stocks['今日主力净流入-净额'].apply(format_amount)
                display_columns.append('主力净流入-净额')
                headers.append('今日主力净流入-净额')
                
                # 处理净占比列
                if '今日主力净流入-净占比' in display_stocks.columns:
                    # 检查是否有有意义的占比数据（非零且在合理范围内）
                    valid_ratios = display_stocks['今日主力净流入-净占比'][
                        (display_stocks['今日主力净流入-净占比'] != 0) & 
                        (display_stocks['今日主力净流入-净占比'].abs() <= 100)
                    ]
                    if len(valid_ratios) > 0:
                        display_stocks['主力净流入-净占比'] = display_stocks['今日主力净流入-净占比'].apply(
                            lambda x: f"{x:.2f}%" if pd.notna(x) and abs(x) <= 100 else 'N/A')
                        display_columns.append('主力净流入-净占比')
                        headers.append('今日主力净流入-净占比')
                
                # 显示表格
                max_col_widths = [4, 8, 8, 8, 6, 10, 8]  # 调整列宽以适应新增的代码和价格列
                print(tabulate(display_stocks[display_columns], headers=headers, tablefmt='psql',
                              showindex=False, maxcolwidths=max_col_widths))

            except Exception as e:
                print(f"个股资金流分析失败: {e}")

        # 4.5 显示涨停股池分析
        if current_limit_up_stocks:
            print(f"\n--- 涨停股池 Top (模拟时间点: {current_sim_time_obj}) ---")
            
            # 显示涨停变化和统计信息
            previous_limit_up_stocks = getattr(run_analysis_backtest, 'previous_limit_up_stocks', [])
            display_limit_up_changes_and_stats(current_limit_up_stocks, previous_limit_up_stocks)
            
            # 显示涨停股池表格
            stock_flow_data_for_limit_up = None
            if latest_rank_file and 'df_stocks' in locals():
                stock_flow_data_for_limit_up = df_stocks
            display_limit_up_pool_table(current_limit_up_stocks, stock_flow_data_for_limit_up)
            
            # 【新增：涨停池关键行业和概念内部分析】
            key_industries, key_concepts = extract_limit_up_key_sectors(current_limit_up_stocks)
            
            if key_industries or key_concepts:
                print(f"\n=== 涨停池热点行业/概念内部结构分析 ===")
                
                # 分析关键行业
                if key_industries:
                    print(f"\n--- 涨停池热点行业内部分析 ---")
                    for idx, industry_name in enumerate(key_industries, 1):
                        print(f"\n【涨停热点行业 {idx}】{industry_name}")
                        # 计算该行业在涨停池中的流入总量（简化估算）
                        industry_stocks = [stock for stock in current_limit_up_stocks 
                                         if stock.get('所属行业', '') == industry_name]
                        estimated_inflow = len(industry_stocks) * 0.5  # 简化估算，每只涨停股0.5亿流入
                        
                        internal_report, _ = analyze_sector_internal_flow_wrapper(
                            industry_name, '行业', estimated_inflow, current_sim_time_obj, data_dir)
                        print(internal_report)
                
                # 分析关键概念
                if key_concepts:
                    print(f"\n--- 涨停池热点概念内部分析 ---")
                    for idx, concept_name in enumerate(key_concepts, 1):
                        print(f"\n【涨停热点概念 {idx}】{concept_name}")
                        # 计算该概念在涨停池中的流入总量（简化估算）
                        concept_stocks_count = 0
                        for stock in current_limit_up_stocks:
                            stock_name = stock.get('名称', '')
                            sectors_info = get_stock_sectors(stock_name)
                            if sectors_info and sectors_info.get('concepts'):
                                if concept_name in sectors_info.get('concepts', []):
                                    concept_stocks_count += 1
                        
                        estimated_inflow = concept_stocks_count * 0.5  # 简化估算，每只涨停股0.5亿流入
                        
                        internal_report, _ = analyze_sector_internal_flow_wrapper(
                            concept_name, '概念', estimated_inflow, current_sim_time_obj, data_dir)
                        print(internal_report)
            else:
                print(f"\n=== 涨停池热点行业/概念内部结构分析 ===")
                print("未发现具有分析价值的热点行业或概念（需要至少2只涨停股）")
            
            # 保存当前涨停股池数据供下次对比
            run_analysis_backtest.previous_limit_up_stocks = current_limit_up_stocks
        else:
            print(f"\n--- 涨停股池 Top (模拟时间点: {current_sim_time_obj}) ---")
            print("暂无涨停股池数据。")

        # 5. 执行板块/概念分析
        for file_type, latest_file, file_list in [('概念', latest_concept_file, concept_files),
                                                  ('行业', latest_industry_file, industry_files)]:
            if latest_file:
                try:
                    df_sectors = pd.read_csv(os.path.join(data_dir, latest_file), encoding='utf-8-sig')
                    # 统一列名
                    df_sectors.rename(
                        columns={'行业': '名称', '行业-涨跌幅': '今日涨跌幅', '净额': '今日主力净流入-净额'},
                        inplace=True)
                    df_sectors['今日主力净流入-净额'] = df_sectors['今日主力净流入-净额'].apply(
                        lambda x: convert_to_float(x) * 1e8 if isinstance(x, str) and '亿' in x else convert_to_float(
                            x))

                    # 【新增：过滤无意义概念和行业】
                    if '名称' in df_sectors.columns:
                        df_sectors = df_sectors[df_sectors['名称'].apply(is_meaningful_concept)]
                        print(f"[DEBUG] 过滤后{file_type}数量: {len(df_sectors)}")

                    # 打印板块排行榜
                    print(f"\n--- {file_type}资金流入 Top 10 ---")
                    display_sectors = df_sectors.head(10).copy()
                    display_sectors['主力净流入'] = display_sectors['今日主力净流入-净额'].apply(format_amount)
                    print(tabulate(display_sectors[['名称', '主力净流入']], headers='keys', tablefmt='psql',
                                   showindex=False))

                    # 对Top10板块进行内部结构分析 + 买入信号检测
                    print(f"\n=== {file_type}资金流入 Top 10 内部格局分析 ===")
                    for idx, (_, row) in enumerate(display_sectors.head(10).iterrows(), 1):
                        leader_name = row['名称']
                        total_inflow_yi = row['今日主力净流入-净额'] / 1e8
                        
                        # 只对前3名进行买入信号检测
                        enable_signals = (idx <= MAX_CONCEPT_RANK if file_type == '概念' else idx <= MAX_INDUSTRY_RANK)
                        
                        print(f"\n【第{idx}名】{leader_name} - 总流入: {total_inflow_yi:.2f}亿")
                        internal_report, sector_buy_signals = analyze_sector_internal_flow_wrapper(
                            leader_name, file_type, total_inflow_yi, current_sim_time_obj, data_dir, 
                            enable_buy_signals=enable_signals, backtester=backtester)
                        print(internal_report)
                        
                        # 收集买入信号 - 直接传入已知排名
                        if sector_buy_signals:
                            daily_buy_signals.extend(sector_buy_signals)
                        elif enable_signals:
                            # 如果包装函数没有检测到信号，直接基于排名进行简单检测
                            try:
                                # 从内部报告中提取结构信息
                                lines = internal_report.split('\n')
                                structure_type = None
                                core_stocks = []
                                
                                for line in lines:
                                    if '内部格局:' in line:
                                        start = line.find('【') + 1
                                        end = line.find('】')
                                        if start > 0 and end > start:
                                            structure_type = line[start:end]
                                    elif '→' in line and '(' in line:
                                        stock_part = line.split('→')[1].strip() if '→' in line else ''
                                        if '(' in stock_part:
                                            stock_name = stock_part.split('(')[0].strip()
                                            if stock_name and stock_name not in core_stocks:
                                                core_stocks.append(stock_name)
                                
                                if structure_type and core_stocks:
                                    # 获取板块内部数据用于价格查询
                                    sector_internal_data = None
                                    try:
                                        if file_type == '概念':
                                            file_path = find_concept_summary_file(leader_name, current_sim_time_obj, data_dir)
                                        else:  # 行业
                                            file_path = find_sector_summary_file(leader_name, current_sim_time_obj, data_dir)

                                        if file_path:
                                            sector_internal_data = parse_internal_data(os.path.join(data_dir, file_path))
                                    except Exception as e:
                                        print(f"[DEBUG] 获取板块内部数据失败: {e}")

                                    manual_signals = detect_buy_signals_from_analysis(
                                        leader_name, structure_type, core_stocks, current_sim_time_obj, data_dir,
                                        backtester, sector_rank=idx,  # 传入当前已知的排名
                                        sector_internal_data=sector_internal_data  # 传递板块内部数据
                                    )
                                    daily_buy_signals.extend(manual_signals)
                                    sector_buy_signals = manual_signals
                                    
                            except Exception as e:
                                print(f"手动信号检测失败: {e}")
                            
                        # 立即执行买入信号
                        if sector_buy_signals:
                            if 'df_stocks' in locals():
                                successful_buys = execute_buy_signals(sector_buy_signals, df_stocks, current_sim_time_obj, backtester)
                                if successful_buys > 0:
                                    print(f"✅ 成功执行{successful_buys}个买入信号")
                            else:
                                print("⚠️ 无法执行买入信号：缺少股票价格数据")

                except Exception as e:
                    print(f"{file_type}板块分析失败: {e}")

    # ============= 回测日终总结 =============
    print("\n" + "=" * 80)
    print("📊 回测日终总结")
    print("=" * 80)
    
    # 计算收盘价（简化处理，使用最新价作为收盘价）
    eod_prices = {}
    if 'df_stocks' in locals() and not df_stocks.empty:
        for _, row in df_stocks.iterrows():
            stock_code = row.get('代码', f"UNKNOWN_{row['名称']}")
            stock_price = row.get('最新价', 0)
            if stock_price > 0:
                eod_prices[stock_code] = stock_price
    
    # 计算当日盈亏
    if eod_prices:
        total_pnl, total_equity, position_details = backtester.calculate_eod_pnl(eod_prices)
        
        print(f"\n📈 当日交易总结:")
        print(f"  总买入信号数: {len(daily_buy_signals)}")
        print(f"  成功买入股数: {len(backtester.positions)}")
        print(f"  当日盈亏: {total_pnl:+,.0f} ({(total_pnl/backtester.total_capital)*100:+.2f}%)")
        print(f"  总权益: {total_equity:,.0f}")
        print(f"  剩余现金: {backtester.cash:,.0f}")
        
        if position_details:
            print(f"\n🎯 持仓盈亏明细:")
            detail_table = []
            for detail in position_details:
                detail_table.append([
                    detail['name'],
                    detail['code'],
                    f"{detail['shares']:,}",
                    f"{detail['buy_price']:.2f}",
                    f"{detail['eod_price']:.2f}",
                    f"{detail['pnl']:+,.0f}",
                    f"{detail['pnl_pct']:+.2f}%",
                    detail['signal_type']
                ])
            
            print(tabulate(detail_table,
                         headers=['股票名称', '代码', '股数', '买入价', '收盘价', '盈亏', '收益率', '信号类型'],
                         tablefmt='grid'))
    
    # 生成完整的回测报告
    backtester.generate_summary_report()
    
    print(f"\n🎉 {date_str} 回测分析完成！")
    return backtester


# ==============================================================================
# § E. 程序入口
# ==============================================================================

def test_concept_filter():
    """测试概念和行业过滤功能"""
    print("=== 概念和行业过滤功能测试 ===")
    
    # 测试用例
    test_concepts = [
        '人工智能', '昨日涨停', '芯片概念', '融资融券',
        '新能源汽车', '央视50', '医疗器械', 'ST板块',
        'TDX 信息', 'TDX 材料', 'tdx 消费', '养老概念'
    ]
    
    print("原始概念列表:", test_concepts)
    filtered = filter_meaningful_concepts_and_sectors(test_concepts)
    print("过滤后概念列表:", filtered)
    
    print("\n单个概念检测:")
    for concept in test_concepts:
        is_meaningful = is_meaningful_concept(concept)
        print(f"  {concept}: {'有意义' if is_meaningful else '无意义'}")
    
    print("\n无意义项目总数:", len(get_meaningless_items()))

if __name__ == "__main__":
    # 如果命令行参数包含 --test-filter，则运行过滤测试
    import sys
    if '--test-filter' in sys.argv:
        test_concept_filter()
    else:
        run_analysis_backtest(BACKTEST_DATE)